---
# Example vault file for initial setup passwords
# Copy this file to init_vault.yml and encrypt with ansible-vault
# 
# Usage:
#   cp init_vault.yml.example init_vault.yml
#   # Edit the file with your actual passwords
#   ansible-vault encrypt init_vault.yml

# Initial Setup Credentials (used during first run)
vault_init_aule_password: "changeme"
vault_init_root_password: "changeme"

# New Passwords (to be set after initial setup)
# IMPORTANT: Change these to secure passwords before encrypting!
vault_new_aule_password: "NewSecureAulePassword123!"
vault_new_root_password: "NewSecureRootPassword456!"

# User Account Passwords (for users created during setup)
# These will be set for users that need password authentication
vault_user_passwords:
  # Primary admin user - krizzo
  krizzo: "KrizzoSecurePassword789!"
  
  # Family member - srizzo (has sudo access)
  srizzo: "SrizzoSecurePassword012!"
  
  # Family member - lrizzo (limited access)
  lrizzo: "LrizzoSecurePassword345!"
  
  # Family member - trizzo (standard user)
  trizzo: "TrizzoSecurePassword678!"
  
  # Family member - frizzo (standard user)
  frizzo: "FrizzoSecurePassword901!"

# Password Policy Settings
vault_password_policy:
  # Force password change on first login for new users
  force_password_change: true
  
  # Password expiration (days)
  password_expire_days: 90
  
  # Minimum password age (days)
  password_min_age: 1
  
  # Password history (prevent reuse of last N passwords)
  password_history: 5
  
  # Account lockout settings
  account_lockout:
    max_failed_attempts: 5
    lockout_duration: 300  # 5 minutes
    
# SSH Configuration
vault_ssh_config:
  # Disable password authentication after setup (force key-only)
  disable_password_auth: true
  
  # Disable root SSH login
  disable_root_ssh: true
  
  # SSH port (change from default 22 for security)
  ssh_port: 22  # Change this to a non-standard port if desired

# Security Settings
vault_security_settings:
  # Enable automatic security updates
  auto_security_updates: true
  
  # Configure fail2ban
  fail2ban_enabled: true
  
  # Configure firewall
  firewall_enabled: true
  
  # Disable unused services
  disable_unused_services: true
