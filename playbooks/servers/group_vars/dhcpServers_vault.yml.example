---
# Example vault file for sensitive DHCP server variables
# Copy this file to dhcpServers_vault.yml and encrypt with ansible-vault
# 
# Usage:
#   cp dhcpServers_vault.yml.example dhcpServers_vault.yml
#   # Edit the file with your actual passwords
#   ansible-vault encrypt dhcpServers_vault.yml

# KEA API Credentials
# IMPORTANT: Change this to a secure password!
vault_kea_api_password: "Kea@DHCP#2024$Secure!"
vault_kea_api_username: "keadm"

# Monitoring Credentials (if using external monitoring)
vault_monitoring_api_key: "your_monitoring_api_key_here"

# Backup Encryption Keys
vault_backup_encryption_key: "your_backup_encryption_key_here"

# SNMP Community Strings (if using SNMP monitoring)
vault_snmp_community_ro: "your_readonly_community_here"
vault_snmp_community_rw: "your_readwrite_community_here"

# Database Credentials (if using external database for leases)
vault_db_username: "kea_user"
vault_db_password: "your_db_password_here"

# SSL/TLS Certificates (if using HTTPS for control agent)
vault_ssl_cert_path: "/etc/ssl/certs/kea-server.crt"
vault_ssl_key_path: "/etc/ssl/private/kea-server.key"
vault_ssl_ca_path: "/etc/ssl/certs/ca-bundle.crt"

# External Service API Keys
vault_dns_api_key: "your_dns_api_key_here"
vault_ipam_api_key: "your_ipam_api_key_here"

# Example strong passwords (replace with your own):
# vault_kea_api_password: "Kea@DHCP#2024$Secure!"
# vault_monitoring_api_key: "Mon1t0r!ng@K3y#2024"
# vault_backup_encryption_key: "B@ckup3ncrypt!0n#K3y2024"
