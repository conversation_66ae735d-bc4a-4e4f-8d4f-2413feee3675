---
# Validation playbook for restructured users.yaml configuration
- hosts: localhost
  connection: local
  gather_facts: false
  
  vars_files:
    - global_vars/users.yaml
  
  tasks:
    - name: Display configuration overview
      ansible.builtin.debug:
        msg:
          - "=== Users Configuration Validation ==="
          - "Total users defined: {{ users_list | length }}"
          - "Users with SSH access: {{ users_list | selectattr('ssh', 'defined') | selectattr('ssh', 'equalto', true) | list | length }}"
          - "Users with sudo access: {{ users_list | selectattr('sudoer', 'defined') | selectattr('sudoer', 'equalto', true) | list | length }}"
          - "Required groups: {{ users_required_groups | length }}"

    - name: Validate user structure
      ansible.builtin.assert:
        that:
          - user_item.name is defined
          - user_item.name | length > 0
          - user_item.id is defined
          - user_item.id | string | length > 0
          - user_item.shell is defined
          - user_item.create_home is defined
        fail_msg: "User {{ user_item.name | default('undefined') }} has invalid structure"
        success_msg: "User {{ user_item.name }} structure is valid"
      loop: "{{ users_list }}"
      loop_control:
        loop_var: user_item
        label: "{{ user_item.name | default('undefined') }}"

    - name: Validate UID uniqueness
      ansible.builtin.assert:
        that:
          - users_list | map(attribute='id') | list | unique | length == users_list | length
        fail_msg: "Duplicate UIDs found in users_list"
        success_msg: "All UIDs are unique"

    - name: Validate username uniqueness
      ansible.builtin.assert:
        that:
          - users_list | map(attribute='name') | list | unique | length == users_list | length
        fail_msg: "Duplicate usernames found in users_list"
        success_msg: "All usernames are unique"

    - name: Validate SSH configuration
      ansible.builtin.assert:
        that:
          - user_item.ssh_pub_keys is defined
          - user_item.ssh_pub_keys | length > 0
          - user_item.ssh_pub_keys | select('match', '^ssh-') | list | length == user_item.ssh_pub_keys | length
        fail_msg: "User {{ user_item.name }} has invalid SSH key configuration"
        success_msg: "User {{ user_item.name }} SSH configuration is valid"
      loop: "{{ users_list | selectattr('ssh', 'defined') | selectattr('ssh', 'equalto', true) | list }}"
      loop_control:
        loop_var: user_item
        label: "{{ user_item.name }}"

    - name: Validate sudo configuration
      ansible.builtin.assert:
        that:
          - user_item.sudo_permission is defined
          - user_item.sudo_permission | length > 0
        fail_msg: "User {{ user_item.name }} has sudoer=true but no sudo_permission defined"
        success_msg: "User {{ user_item.name }} sudo configuration is valid"
      loop: "{{ users_list | selectattr('sudoer', 'defined') | selectattr('sudoer', 'equalto', true) | list }}"
      loop_control:
        loop_var: user_item
        label: "{{ user_item.name }}"

    - name: Validate shell paths
      ansible.builtin.assert:
        that:
          - user_item.shell | regex_search('^/') is not none
        fail_msg: "User {{ user_item.name }} has invalid shell path: {{ user_item.shell }}"
        success_msg: "User {{ user_item.name }} shell path is valid"
      loop: "{{ users_list }}"
      loop_control:
        loop_var: user_item
        label: "{{ user_item.name }}"

    - name: Validate UID ranges
      ansible.builtin.assert:
        that:
          - user_item.id | int >= 1000
          - user_item.id | int <= 65534
        fail_msg: "User {{ user_item.name }} UID {{ user_item.id }} is outside valid range (1000-65534)"
        success_msg: "User {{ user_item.name }} UID is in valid range"
      loop: "{{ users_list }}"
      loop_control:
        loop_var: user_item
        label: "{{ user_item.name }}"

    - name: Display user summary
      ansible.builtin.debug:
        msg:
          - "User: {{ user_item.name }}"
          - "UID: {{ user_item.id }}"
          - "Shell: {{ user_item.shell }}"
          - "SSH: {{ user_item.ssh | default(false) }}"
          - "Sudo: {{ user_item.sudoer | default(false) }}"
          - "Groups: {{ user_item.groups | default([]) | join(', ') }}"
          - "SSH Keys: {{ user_item.ssh_pub_keys | default([]) | length }}"
      loop: "{{ users_list }}"
      loop_control:
        loop_var: user_item
        label: "{{ user_item.name }}"

    - name: Validate global settings structure
      ansible.builtin.assert:
        that:
          - users_global_settings is defined
          - users_global_settings.default_shell is defined
          - users_sudo_templates is defined
          - users_required_groups is defined
        fail_msg: "Global settings structure is invalid"
        success_msg: "Global settings structure is valid"

    - name: Display validation results
      ansible.builtin.debug:
        msg:
          - "✅ Users configuration validation completed successfully!"
          - "✅ All user structures are valid"
          - "✅ No duplicate UIDs or usernames"
          - "✅ SSH configurations are valid"
          - "✅ Sudo configurations are valid"
          - "✅ Shell paths are valid"
          - "✅ UID ranges are appropriate"
          - "✅ Global settings are properly structured"
          - ""
          - "Configuration is ready for deployment with optimized users and sudoers roles!"
