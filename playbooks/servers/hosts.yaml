# vim: set syntax=yaml:
---
all:
  hosts:
  children:
    linuxServers:
      children:
        DEBServers:
        RELServers:
    DEBServers:
      vars:
        # Because ansible fails due to maintaining a list of distros and paths to python.
        # ansible_python_interpreter: "/usr/bin/python3"
      hosts:
        silmarillion-003: # Network Services bind9 and pih<PERSON>, unifi controller
          ansible_host: *************
        blueberry<PERSON>:
          ansible_host: *************
        merry: # Minecraft Server
          ansible_host: *************
        retro-games: # Emulation computer
          ansible_host: *************
        docker-host-001:
          ansible_host: *************
        lrizzo-linux-vm:
          ansible_host: **************
        trizzo-linux-vm:
          ansible_host: **************
        krizzo-linux-vm:
          ansible_host: **************
        lrizzo-pi5:
          ansible_host: ************6
        trizzo-pi5:
          ansible_host: *************
        annatar: # IPXE server FOG Server
          ansible_host: ************
        palantir: # Frigate NVR
          ansible_host: *************
        moria: # Media Miner
          ansible_host: *************
#        ospi:
#          ansible_host: *************8
      children:
        dhcpServers:
        debTestSerers:
    RELServers:
      vars:
        # Because ansible fails due to maintaining a list of distros and paths to python.
        # Deb hosts need sudo installed and user to sudo access
        # ansible_python_interpreter: "/usr/bin/python3"
      hosts:
        plex:
          ansible_host: *************
        pippin: # Sourcer
          ansible_host: *************
        gandalf: #xenOrchestra
          ansible_host: *************
    dhcpServers:
      hosts:
        silmarillion-001: # Network Services DHCP/DNS
          ansible_host: ************
        silmarillion-002: # Network Services DHCP/DNS
          ansible_host: ************
      vars:
        network_gateway: ************
        primary_ip: ************
        primary_hostname: silmarillion-001
        secondary_ip: ************
        secondary_hostname: silmarillion-002
    dnsServers:
      hosts:
        dns-primary:
          ansible_host: ************
        dns-secondary:
          ansible_host: ************
    networkServices:
      children:
        dnsServers:
        dhcpServers:
    testServers:
      children:
        debTestSerers:
    dhcpTestServers:
      hosts:
        dns-primary:
          ansible_host: **************
        dns-secondary:
          ansible_host: **************
      vars:
        primary_ip: **************
        primary_hostname: silmarillion-001
        secondary_ip: **************
        secondary_hostname: silmarillion-002
    debTestSerers:
      hosts:
        deb-001:
          ansible_host: ************91
        deb-002:
          ansible_host: ************92
        deb-003:
          ansible_host: ************93
    debClients:
      hosts:
        krizzo-w530:
          ansible_host: ************42
    workServers:
      hosts:
        dan-dhcp-01-vm:
          ansible_host: **********
        dan-dhcp-02-vm:
          ansible_host: **********
        dan-test-01-vm:
          ansible_host: ***********
        dan-test-02-vm:
          ansible_host: ***********
        dan-test-03-vm:
          ansible_host: ***********
