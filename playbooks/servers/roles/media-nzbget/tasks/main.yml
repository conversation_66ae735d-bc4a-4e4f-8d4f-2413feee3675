---
# tasks file for media-nzbget

- name: "Mount {{ item.name }}"
  ansible.posix.mount:
    src: "{{ item.src }}"
    path: "{{ item.path }}"
    opts: "{{ item.opts }}"
    state: mounted
    fstype: "{{ item.fstype }}"
  loop: "{{ nfs_mounts }}"

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop: "{{ app_paths }}"

- name: Configure firewall rules for NZBGet
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ nzbget_port }}"
      protocol: tcp
      comment: "expose port for nzbget ui http"

- name: Pull the latest NZBGet image
  containers.podman.podman_image:
    name: "{{ nzbget_image }}"
  register: nzbget_image_pull

- name: Stop and remove existing nzbget container
  containers.podman.podman_container:
    name: "{{ nzbget_container_name }}"
    state: absent
  when: nzbget_image_pull.changed

# NZB Download App
- name: Start nzbget container
  containers.podman.podman_container:
    name: "{{ nzbget_container_name }}"
    hostname: "{{ nzbget_hostname }}"
    image: "{{ nzbget_image }}"
    state: started
    restart_policy: "on-failure:5"
    network: bridge
    ports:
      - "{{ nzbget_port }}:{{ nzbget_port }}"
    volumes: "{{ nzbget_volumes }}"
    env: "{{ nzbget_environment }}"
