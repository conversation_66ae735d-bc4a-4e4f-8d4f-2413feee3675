---
# tasks file for users

- name: Validate users_list variable
  ansible.builtin.assert:
    that:
      - users_list is defined
      - users_list is iterable
      - users_list | length > 0
    fail_msg: "users_list must be defined and contain at least one user"
    success_msg: "users_list validation passed"
  tags: ['users', 'validation']

- name: Validate user configurations
  ansible.builtin.assert:
    that:
      - user_item.name is defined
      - user_item.name | length > 0
      - user_item.id is defined
      - user_item.id | int > 0
    fail_msg: "User {{ user_item.name | default('undefined') }} has invalid configuration"
    success_msg: "User {{ user_item.name }} configuration is valid"
  loop: "{{ users_list }}"
  loop_control:
    loop_var: user_item
    label: "{{ user_item.name | default('undefined') }}"
  tags: ['users', 'validation']

- name: Gather system user and group information (once)
  ansible.builtin.setup:
    gather_subset:
      - "!all"
      - "!min"
      - "user_dir"
  when: users_gather_facts_once | bool
  tags: ['users', 'facts']

- name: Get existing users and groups
  block:
    - name: Get available groups
      ansible.builtin.getent:
        database: group

    - name: Get available users
      ansible.builtin.getent:
        database: passwd
  tags: ['users', 'facts']

- name: Process users in batches
  ansible.builtin.include_tasks: process_user.yml
  loop: "{{ users_list }}"
  loop_control:
    loop_var: user_config
    label: "{{ user_config.name }}"
  tags: ['users', 'create']

- name: Configure user environments
  ansible.builtin.include_tasks: configure_user_environment.yml
  loop: "{{ users_list }}"
  loop_control:
    loop_var: user_config
    label: "{{ user_config.name }}"
  when: user_config.shell is defined
  tags: ['users', 'environment']
