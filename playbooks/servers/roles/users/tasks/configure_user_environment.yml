---
# Configure user environment and shell settings

- name: Set environment facts for {{ user_config.name }}
  ansible.builtin.set_fact:
    user_shell: "{{ user_config.shell | default(users_default_shell) }}"
    user_home: "/home/<USER>"
    shell_config: "{{ users_shell_configs[user_config.shell | default(users_default_shell)] | default({}) }}"

- name: Check if shell configuration is supported
  ansible.builtin.debug:
    msg: "Shell {{ user_shell }} configuration available: {{ shell_config | length > 0 }}"
  when: ansible_verbosity >= 1

- name: Configure shell environment for {{ user_config.name }}
  block:
    - name: Check if shell config file exists
      ansible.builtin.stat:
        path: "{{ user_home }}/{{ shell_config.config_file }}"
      register: existing_config

    - name: Backup existing shell configuration
      ansible.builtin.copy:
        src: "{{ user_home }}/{{ shell_config.config_file }}"
        dest: "{{ user_home }}/{{ shell_config.config_file }}{{ users_backup_suffix }}"
        remote_src: true
        owner: "{{ user_config.name }}"
        group: "{{ user_config.name }}"
        mode: preserve
      when: 
        - existing_config.stat.exists
        - users_backup_existing_configs | bool

    - name: Deploy shell configuration template
      ansible.builtin.template:
        src: "{{ shell_config.template }}"
        dest: "{{ user_home }}/{{ shell_config.config_file }}"
        owner: "{{ user_config.name }}"
        group: "{{ user_config.name }}"
        mode: '0644'
        backup: "{{ users_backup_existing_configs }}"
      when: shell_config.template is defined

    - name: Check if default shell configuration file exists
      ansible.builtin.stat:
        path: "{{ role_path }}/files/{{ shell_config.config_file | regex_replace('^\\.', '') }}.default"
      register: default_config_file
      when:
        - shell_config.template is not defined
        - not existing_config.stat.exists

    - name: Deploy default shell configuration (fallback)
      ansible.builtin.copy:
        src: "{{ shell_config.config_file | regex_replace('^\\.', '') }}.default"
        dest: "{{ user_home }}/{{ shell_config.config_file }}"
        owner: "{{ user_config.name }}"
        group: "{{ user_config.name }}"
        mode: '0644'
        force: false
      when:
        - shell_config.template is not defined
        - not existing_config.stat.exists
        - default_config_file.stat.exists | default(false)

  when:
    - shell_config | length > 0
    - user_config.name in getent_passwd | default({})

- name: Create additional user directories
  ansible.builtin.file:
    path: "{{ user_home }}/{{ directory }}"
    state: directory
    owner: "{{ user_config.name }}"
    group: "{{ user_config.name }}"
    mode: '0755'
  loop:
    - "bin"
    - ".local"
    - ".local/bin"
    - ".config"
  loop_control:
    loop_var: directory
  when: user_config.create_directories | default(true)

- name: Set user environment variables
  ansible.builtin.lineinfile:
    path: "{{ user_home }}/.profile"
    line: "export {{ env_var.key }}={{ env_var.value }}"
    regexp: "^export {{ env_var.key }}="
    create: true
    owner: "{{ user_config.name }}"
    group: "{{ user_config.name }}"
    mode: '0644'
  loop: "{{ user_config.environment | default({}) | dict2items }}"
  loop_control:
    loop_var: env_var
    label: "{{ env_var.key }}"
  when: user_config.environment is defined

- name: Add user to additional groups
  ansible.builtin.user:
    name: "{{ user_config.name }}"
    groups: "{{ user_config.additional_groups | join(',') }}"
    append: true
  when: 
    - user_config.additional_groups is defined
    - user_config.additional_groups | length > 0

- name: Display environment configuration summary
  ansible.builtin.debug:
    msg:
      - "User: {{ user_config.name }}"
      - "Shell: {{ user_shell }}"
      - "Config file: {{ shell_config.config_file | default('none') }}"
      - "Template used: {{ shell_config.template | default('default file') }}"
      - "Environment vars: {{ user_config.environment | default({}) | length }}"
      - "Additional groups: {{ user_config.additional_groups | default([]) | length }}"
  when: ansible_verbosity >= 1
