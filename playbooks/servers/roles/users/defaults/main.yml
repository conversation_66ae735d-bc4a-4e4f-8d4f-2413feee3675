---
# defaults file for users

# Default user configuration
users_default_shell: "/bin/bash"
users_default_create_home: true
users_default_ssh_key_type: "ed25519"
users_default_ssh_key_bits: 4096

# System configuration
users_ssh_dir_mode: "0700"
users_ssh_authorized_keys_file: "authorized_keys"
users_ssh_authorized_keys_mode: "0600"

# User ID ranges as strings
users_system_uid_min: "1000"
users_system_uid_max: "60000"
users_system_gid_min: "1000"
users_system_gid_max: "60000"

# Shell configuration files
users_shell_configs:
  "/bin/bash":
    config_file: ".bashrc"
    template: "bashrc.j2"
  "/usr/bin/zsh":
    config_file: ".zshrc"
    template: "zshrc.j2"
  "/bin/zsh":
    config_file: ".zshrc"
    template: "zshrc.j2"

# Default groups to add users to (optional)
users_default_groups: []

# Backup configuration
users_backup_existing_configs: true
users_backup_suffix: ".ansible-backup"

# Validation settings
users_validate_uid_gid: true
users_allow_duplicate_ids: false

# Performance settings
users_batch_operations: true
users_gather_facts_once: true
