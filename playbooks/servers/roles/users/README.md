Users Management Role
====================

An optimized Ansible role for managing system users with advanced features including shell configuration, SSH key management, and environment setup.

Requirements
------------

- Ansible 2.9 or higher
- `ansible.posix` collection (for authorized_key module)
- Target systems: Linux (Debian/Ubuntu, RHEL/CentOS/Rocky)
- Sudo privileges for user management

Features
--------

- **Efficient Processing**: Optimized loops and batch operations
- **Validation**: Comprehensive UID/GID conflict detection
- **Shell Configuration**: Automated shell setup with templates
- **SSH Management**: Secure SSH key deployment
- **Environment Setup**: Custom environment variables and directories
- **Idempotency**: Safe to run multiple times
- **Error Handling**: Robust validation and error recovery

Role Variables
--------------

### Required Variables

- `users_list`: List of user configurations (see example below)

### Default Variables (defaults/main.yml)

```yaml
# Default user configuration
users_default_shell: "/bin/bash"
users_default_create_home: true

# System configuration
users_ssh_dir_mode: "0700"
users_ssh_authorized_keys_mode: "0600"

# User ID ranges
users_system_uid_min: 1000
users_system_uid_max: 60000

# Shell configurations
users_shell_configs:
  "/bin/bash":
    config_file: ".bashrc"
    template: "bashrc.j2"
  "/usr/bin/zsh":
    config_file: ".zshrc"
    template: "zshrc.j2"

# Validation settings
users_validate_uid_gid: true
users_backup_existing_configs: true
```

### User Configuration Format

```yaml
users_list:
  - name: "username"              # Required: Username
    id: "1000"                    # Required: UID/GID (string or int)
    shell: "/usr/bin/zsh"         # Optional: User shell
    ssh: true                     # Optional: Enable SSH access
    ssh_pub_keys:                 # Optional: SSH public keys
      - "ssh-ed25519 AAAAC3... user@host"
    groups: ["docker", "sudo"]    # Optional: Additional groups
    environment:                  # Optional: Environment variables
      EDITOR: "vim"
      LANG: "en_US.UTF-8"
    create_directories: true      # Optional: Create standard directories
```

Dependencies
------------

- `ansible.posix` collection for SSH key management

Example Playbook
----------------

```yaml
- hosts: servers
  become: true
  vars:
    users_list:
      - name: "krizzo"
        id: "1985"
        shell: "/usr/bin/zsh"
        ssh: true
        ssh_pub_keys:
          - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAtrnC0RHMFtRV5x6hP1ttAyNLQdengHzk1hu0WZUl/U krizzo@laptop"
        groups: ["sudo", "docker"]
        environment:
          EDITOR: "vim"
          LANG: "en_US.UTF-8"
      - name: "developer"
        id: "2000"
        shell: "/bin/bash"
        ssh: true
        ssh_pub_keys:
          - "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAAB... dev@workstation"
  roles:
    - users
```

### Advanced Usage

```yaml
# Custom shell configuration
users_shell_configs:
  "/usr/bin/fish":
    config_file: ".config/fish/config.fish"
    template: "fish_config.j2"

# Performance tuning
users_batch_operations: true
users_gather_facts_once: true

# Security settings
users_validate_uid_gid: true
users_allow_duplicate_ids: false
```

Tags
----

- `users`: All user management tasks
- `validation`: User configuration validation
- `create`: User and group creation
- `environment`: Shell and environment configuration

License
-------

MIT

Author Information
------------------

Maintained by krizzo for home lab infrastructure management.
Optimized for performance and following Ansible best practices.
