---
# defaults file for media-sonarr

# NFS configuration
nfs_server: "192.168.28.31"
media_nfs_path: "/mnt/nasa-p1z2/Media"
media_mount_path: "/mnt/nasa-p1z2-media"
config_nfs_path: "/mnt/nasa-p1z2/docker-data"
config_mount_path: "/mnt/nasa-p1z2-docker-data"

# NFS mounts list for automated mounting
nfs_mounts:
  - name: "Media NFS volume"
    src: "{{ nfs_server }}:{{ media_nfs_path }}"
    path: "{{ media_mount_path }}"
    opts: "rw"
    fstype: "nfs"
    comment: "Main media storage mount"
  - name: "docker-data Config NFS volume"
    src: "{{ nfs_server }}:{{ config_nfs_path }}"
    path: "{{ config_mount_path }}"
    opts: "rw"
    fstype: "nfs"
    comment: "Container configuration storage mount"

# Local paths
sonarr_config_dir: "/mnt/docker-app-configs/sonarr/config"
sonarr_config_backups: "{{ config_mount_path }}/host-{{ ansible_hostname }}/sonarr/config"

# Application paths for directory creation
app_paths:
  - path: "{{ sonarr_config_dir }}"
    comment: "Application config local for speed"
  - path: "{{ sonarr_config_backups }}"
    comment: "Application config backup dir on NFS mount"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"
sonarr_image: "lscr.io/linuxserver/sonarr:latest"
sonarr_port: 8989
sonarr_container_name: "sonarr"
sonarr_hostname: "sonarr"

# Container volumes
sonarr_volumes:
  - "{{ sonarr_config_dir }}:/config"
  - "{{ media_mount_path }}:/data"

# Container environment variables
sonarr_environment:
  PUID: "{{ container_user_id }}"
  PGID: "{{ container_group_id }}"
  TZ: "America/Denver"
