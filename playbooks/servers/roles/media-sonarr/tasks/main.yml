---
# tasks file for media-sonarr

- name: "Mount {{ item.name }}"
  ansible.posix.mount:
    src: "{{ item.src }}"
    path: "{{ item.path }}"
    opts: "{{ item.opts }}"
    state: mounted
    fstype: "{{ item.fstype }}"
  loop: "{{ nfs_mounts }}"

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop: "{{ app_paths }}"

- name: Configure firewall rules for Sonarr
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ sonarr_port }}"
      protocol: tcp
      comment: "expose port for sonarr ui http"

- name: Pull the latest Sonarr image
  containers.podman.podman_image:
    name: "{{ sonarr_image }}"
  register: sonarr_image_pull

- name: Stop and remove existing sonarr container
  containers.podman.podman_container:
    name: "{{ sonarr_container_name }}"
    state: absent
  when: sonarr_image_pull.changed

# TV Series Search App
- name: Start sonarr container
  containers.podman.podman_container:
    name: "{{ sonarr_container_name }}"
    hostname: "{{ sonarr_hostname }}"
    image: "{{ sonarr_image }}"
    state: started
    restart_policy: "on-failure:5"
    network: bridge
    ports:
      - "{{ sonarr_port }}:{{ sonarr_port }}"
    volumes: "{{ sonarr_volumes }}"
    env: "{{ sonarr_environment }}"
