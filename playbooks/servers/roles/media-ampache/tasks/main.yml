#SPDX-License-Identifier: MIT-0
---
# tasks file for media-ampache

- name: Mount Media NFS volume
  ansible.posix.mount:
    src: *************:/mnt/nasa-p1z2/Media
    path: /mnt/nasa-p1z2-media
    opts: rw,soft
    state: mounted
    fstype: nfs

- name: Mount docker-data Config NFS volume
  ansible.posix.mount:
    src: *************:/mnt/nasa-p1z2/docker-data
    path: /mnt/nasa-p1z2-docker-data
    opts: rw,soft
    state: mounted
    fstype: nfs

- name: Create IP tables allow rules
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    source_port: "{{ item.port }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: 8040
      protocol: "tcp"
      comment: "expose port for ampache ui http"

- name: Pull the latest image
  containers.podman.podman_image:
    name: "ampache/ampache"

- name: Stop and remove existing ampache container
  containers.podman.podman_container:
    name: ampache
    state: absent

# Username is admin, password is in container log
- name: Start ampache container
  containers.podman.podman_container:
    name: ampache
    image: "ampache/ampache"
    state: started
    force_restart: yes
    restart_policy: always
    network: bridge
    ports:
      - 8040:80/tcp
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /mnt/nasa-p1z2-media:/media:ro
      - /mnt/nasa-p1z2-docker-data/ampache/mysql:/var/lib/mysql
      - /mnt/nasa-p1z2-docker-data/ampache/config:/var/www/config
      - /mnt/nasa-p1z2-docker-data/ampache/log:/var/log/ampache
    env:
      DISABLE_INOTIFYWAIT_CLEAN: 1 # See https://github.com/ampache/ampache-docker?tab=readme-ov-file#environment-variables
      UMASK: "000"
      PUID: "1000"
      PGID: "1000"

- name: Set correct permissions on config folder
  containers.podman.podman_container_exec:
    name: ampache
    command: "chown 33:33 ./data/config -R ; chown 33:33 ./data/log"
