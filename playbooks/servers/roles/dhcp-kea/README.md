DHCP KEA Role
=============

This Ansible role installs and configures ISC KEA DHCP server with high availability support. The role provides a robust, variable-driven approach to managing DHCP subnets and reservations.

Requirements
------------

- Ansible 2.9 or higher
- Target systems: Debian/Ubuntu or RHEL/Rocky/CentOS
- Network connectivity between primary and secondary DHCP servers for HA
- Proper firewall configuration for DHCP and KEA control agent ports

Role Variables
--------------

### Required Variables

These variables must be defined in your inventory or playbook:

- `kea_api_password`: Password for KEA control agent API
- `primary_ip`: IP address of primary DHCP server
- `secondary_ip`: IP address of secondary DHCP server
- `primary_hostname`: Hostname of primary DHCP server
- `secondary_hostname`: Hostname of secondary DHCP server
- `network_gateway`: Default gateway for the network

### Default Variables

The role includes comprehensive defaults in `defaults/main.yml`:

- `kea_subnets`: List of subnet configurations with pools and reservations
- `kea_global_config`: Global DHCP settings (lease times, database config, etc.)
- `kea_ha_config`: High availability configuration
- `kea_firewall_ports`: Firewall ports to open for DHCP services

### Subnet Configuration

Subnets are defined as a list in `kea_subnets`. Each subnet includes:

```yaml
kea_subnets:
  - id: 28
    name: "trust"
    description: "Trust Network"
    subnet: "************/23"
    subnet_mask: "*************"
    gateway: "************"
    dns_servers: "*******, *******"
    valid_lifetime: 86400
    max_valid_lifetime: 259200
    pools:
      - "************60 - **************"
      - "************** - **************"
    reservations:
      - hostname: "server-001"
        hw_address: "aa:bb:cc:dd:ee:ff"
        ip_address: "************0"
        comment: "Optional comment"
```

### Network Consolidation

This role consolidates the previous subnet 28 (/24) and subnet 29 (/24) into a single subnet 28 (/23) for easier management. All IoT devices previously in subnet 29 are now part of the trust network.

Dependencies
------------

- `community.general` collection (for nmcli module)
- `ansible.posix` collection (for firewalld module on RHEL systems)

Example Playbook
----------------

```yaml
- hosts: dhcp_servers
  become: yes
  vars:
    kea_api_password: "secure_password_here"
    primary_ip: "*************"
    secondary_ip: "*************"
    primary_hostname: "silmarillion-001"
    secondary_hostname: "silmarillion-002"
    network_gateway: "************"
  roles:
    - dhcp-kea
```

### Adding New Subnets

To add a new subnet, simply add it to the `kea_subnets` list in your variables:

```yaml
kea_subnets:
  # ... existing subnets ...
  - id: 32
    name: "new-network"
    description: "New Network Segment"
    subnet: "************/24"
    subnet_mask: "*************"
    gateway: "************"
    dns_servers: "*******, *******"
    valid_lifetime: 86400
    max_valid_lifetime: 259200
    pools:
      - "************** - **************"
    reservations: []
```

### Managing Reservations

Reservations are defined within each subnet configuration. The role automatically generates the appropriate KEA configuration files.

License
-------

MIT

Author Information
------------------

Maintained by krizzo for home lab infrastructure management.
