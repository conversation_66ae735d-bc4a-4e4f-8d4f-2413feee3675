---
# tasks file for dhcp-kea

- name: Update DEB system
  ansible.builtin.apt:
    upgrade: yes
    update_cache: yes
    cache_valid_time: 86400 #One day

- name: Install required DEB packages
  ansible.builtin.apt:
    name: "{{ DEB_pkgs }}"
    state: latest
    update_cache: yes

- name: Set primary IP static
  community.general.nmcli:
    conn_name: "{{ ansible_default_ipv4.interface }}"
    ifname: "{{ ansible_default_ipv4.interface }}"
    type: ethernet
    ip4:
      - "{{ primary_ip }}/24"
    gw4: "{{ network_gateway }}"
    dns4:
      - *******
      - "{{ primary_ip }}"
      - "{{ secondary_ip }}"
    state: present
  when: (ansible_facts['hostname']|lower == "silmarillion-001")

- name: Set secondary IP static
  community.general.nmcli:
    conn_name: "{{ ansible_default_ipv4.interface }}"
    ifname: "{{ ansible_default_ipv4.interface }}"
    type: ethernet
    ip4:
      - "{{ secondary_ip }}/24"
    gw4: "{{ network_gateway }}"
    dns4:
      - *******
      - "{{ primary_ip }}"
      - "{{ secondary_ip }}"
    state: present
  when: (ansible_facts['hostname']|lower == "silmarillion-002")

- name: Allow DHCP ports via iptables (nftables)
  ansible.builtin.iptables:
      chain: INPUT
      protocol: "{{ item.protocol }}"
      source_port: "{{ item.port }}"
      destination_port: "{{ item.port }}"
      jump: ACCEPT
      comment: "ALLOW {{ item.comment }}"
  loop: "{{ kea_firewall_ports }}"
