
---
# tasks file for dhcp-kea RHEL/Rocky/CentOS

- name: Update RHEL system
  ansible.builtin.yum:
    name: "*"
    state: latest
    update_cache: yes

- name: Install required RHEL packages
  ansible.builtin.yum:
    name: "{{ RHEL_pkgs }}"
    state: latest
    update_cache: yes

- name: Set primary IP static
  community.general.nmcli:
    conn_name: "{{ ansible_default_ipv4.interface }}"
    ifname: "{{ ansible_default_ipv4.interface }}"
    type: ethernet
    ip4:
      - "{{ primary_ip }}/24"
    gw4: "{{ network_gateway }}"
    dns4:
      - *******
      - "{{ primary_ip }}"
      - "{{ secondary_ip }}"
    state: present
  when: (ansible_facts['hostname']|lower == "silmarillion-001")

- name: Set secondary IP static
  community.general.nmcli:
    conn_name: "{{ ansible_default_ipv4.interface }}"
    ifname: "{{ ansible_default_ipv4.interface }}"
    type: ethernet
    ip4:
      - "{{ secondary_ip }}/24"
    gw4: "{{ network_gateway }}"
    dns4:
      - *******
      - "{{ primary_ip }}"
      - "{{ secondary_ip }}"
    state: present
  when: (ansible_facts['hostname']|lower == "silmarillion-002")

- name: Allow DHCP ports via firewalld
  ansible.posix.firewalld:
    port: "{{ item.port }}/{{ item.protocol }}"
    permanent: yes
    state: enabled
    immediate: yes
  loop: "{{ kea_firewall_ports }}"
  notify: Reload firewalld