#// IPs here should never be in the dynamic pool range with our settings
"reservations": [
{% for reservation in subnet.reservations %}
  {
{% if reservation.comment is defined %}
    #// {{ reservation.comment }}
{% endif %}
    "hostname": "{{ reservation.hostname }}",
    "hw-address": "{{ reservation.hw_address }}",
    "ip-address": "{{ reservation.ip_address }}"
  }{% if not loop.last %},{% endif %}
{% endfor %}
]
