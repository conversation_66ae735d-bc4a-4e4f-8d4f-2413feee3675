---
# handlers file for dhcp-kea
- name: Restart kea-ctrl-agent-service
  ansible.builtin.service:
    name: kea-ctrl-agent
    state: restarted

- name: Restart kea-dhcp4-service
  ansible.builtin.service:
    name: kea-dhcp4-server
    state: restarted

- name: Restart kea-dhcp6-service
  ansible.builtin.service:
    name: kea-dhcp6-server
    state: restarted

- name: Restart kea-dhcp-ddns-service
  ansible.builtin.service:
    name: kea-dhcp-ddns-server
    state: restarted

- name: Reload firewalld
  ansible.builtin.service:
    name: firewalld
    state: reloaded