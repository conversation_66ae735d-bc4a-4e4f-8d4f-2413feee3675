---
# defaults file for dhcp-kea

# Package lists
DEB_pkgs:
  - kea
  - kea-ctrl-agent
  - kea-dhcp4-server
  - kea-dhcp6-server
  - kea-dhcp-ddns-server

RHEL_pkgs:
  - kea
  - kea-ctrl-agent
  - kea-dhcp4-server
  - kea-dhcp6-server
  - kea-dhcp-ddns-server

# KEA API Configuration
kea_api_username: "kea-admin"
kea_api_auth_file: "/etc/kea/kea-api.passwd"

# Global DHCP Configuration
kea_global_config:
  valid_lifetime: 86400  # 1 day
  calculate_tee_times: true
  t1_percent: 0.5
  t2_percent: 0.875
  reservations_global: false
  reservations_in_subnet: true
  reservations_out_of_pool: true
  host_reservation_identifiers:
    - "hw-address"
  lease_database:
    type: "memfile"
    lfc_interval: 3600
    persist: true
    max_row_errors: 100
  expired_leases_processing:
    reclaim_timer_wait_time: 3600  # 1 hour
    hold_reclaimed_time: 172800    # 2 days
    max_reclaim_leases: 0
    max_reclaim_time: 0
  default_dns_servers: "*******, *******"

# High Availability Configuration
kea_ha_config:
  mode: "hot-standby"
  heartbeat_delay: 10000
  max_response_delay: 60000
  max_ack_delay: 5000
  max_unacked_clients: 5
  sync_timeout: 60000

# Firewall ports to allow
kea_firewall_ports:
  - port: 67
    protocol: "udp"
    comment: "DHCPv4 UDP BOOTPS Server"
  - port: 547
    protocol: "udp"
    comment: "DHCPv6 UDP Server"
  - port: 8000
    protocol: "tcp"
    comment: "Kea CA Control Agent HTTP/HTTPS port"

# Subnet Configurations
kea_subnets:
  - id: 24
    name: "guest"
    description: "Guest Network"
    subnet: "**************/25"
    subnet_mask: "***************"
    gateway: "**************"
    dns_servers: "*******, *******"
    valid_lifetime: 28800  # 8 hours for guests
    max_valid_lifetime: 28800
    pools:
      - "************** - **************"
    reservations: []

  - id: 25
    name: "management"
    description: "Management network"
    subnet: "************/24"
    subnet_mask: "*************"
    gateway: "************"
    dns_servers: "*******, *******"
    valid_lifetime: 86400  # 1 day
    max_valid_lifetime: 604800  # 7 days
    pools:
      - "************** - **************"
    reservations:
      - hostname: "U6-Pro-First-Floor"
        hw_address: "e4:38:83:28:d4:e9"
        ip_address: "************1"
      - hostname: "U6-Pro-Second-Floor"
        hw_address: "e4:38:83:28:da:4d"
        ip_address: "************2"
      - hostname: "jetkvm-001"
        hw_address: "30:52:53:0e:a9:29"
        ip_address: "*************"

  - id: 26
    name: "security"
    description: "Security Network"
    subnet: "************/24"
    subnet_mask: "*************"
    gateway: "************"
    dns_servers: "*******, *******"
    valid_lifetime: 259200  # 3 days
    max_valid_lifetime: 604800  # 7 days
    pools:
      - "************** - **************"
    reservations:
      - hostname: "palantir"
        hw_address: "2c:f7:f1:1f:e1:a4"
        ip_address: "************0"
        comment: "frigate-nvr left port viewed from back"
      - hostname: "reolink-doorbell"
        hw_address: "ec:71:db:4b:36:6b"
        ip_address: "************1"
      - hostname: "reolink-familyroom"
        hw_address: "ec:71:db:f3:79:75"
        ip_address: "************2"
      - hostname: "reolink-loft"
        hw_address: "ec:71:db:54:8c:7e"
        ip_address: "************3"
      - hostname: "reolink-main-bedroom"
        hw_address: "ec:71:db:ec:03:2d"
        ip_address: "************4"

  - id: 28
    name: "trust"
    description: "Trust Network (Consolidated /23 - includes former IoT devices)"
    subnet: "************/23"
    subnet_mask: "*************"
    gateway: "************"
    dns_servers: "*******, *******"
    valid_lifetime: 86400  # 1 day
    max_valid_lifetime: 259200  # 3 days
    pools:
      - "**************/25" # 126 dynamic hosts IPs these maybe we want to put in an untruest no internet until checked?
    reservations:
      # Trust network devices (original subnet 28)
      - hostname: "silmarillian-001"
        hw_address: "b2:58:8e:9b:0d:9e"
        ip_address: "************"
        comment: "STATIC IP"
      - hostname: "silmarillian-002"
        hw_address: "3a:78:7c:69:d7:12"
        ip_address: "************"
        comment: "STATIC IP"
      - hostname: "annatar"
        hw_address: "06:e9:41:6a:f8:23"
        ip_address: "************"
        comment: "STATIC IP"
      - hostname: "og01-a01-krizzo01"
        hw_address: "00:13:c6:01:a5:6e"
        ip_address: "************"
      - hostname: "palantir"
        hw_address: "2c:f7:f1:1f:e1:a3"
        ip_address: "************0"
        comment: "frigate-nvr right port viewed from back"
      - hostname: "docker-host-001"
        hw_address: "06:40:1d:7a:f4:b1"
        ip_address: "*************"
      - hostname: "moria"
        hw_address: "be:91:2c:ee:51:78"
        ip_address: "************8"
        comment: "Media Mine"
      - hostname: "gandalf"
        hw_address: "42:2b:68:f2:30:cd"
        ip_address: "*************"
        comment: "STATIC xen-orchestra"
      - hostname: "silmarillian-001"
        hw_address: "dc:a6:32:e7:5d:77"
        ip_address: "*************"
        comment: "STATIC pi-001 see cat /etc/dhcpcd.conf"
      - hostname: "silmarillian-002"
        hw_address: "dc:a6:32:e7:5d:d1"
        ip_address: "*************"
        comment: "STATIC pi-002 see cat /etc/dhcpcd.conf"
      - hostname: "silmarillian-003"
        hw_address: "dc:a6:32:e9:54:2f"
        ip_address: "*************"
        comment: "STATIC pi-003 see cat /etc/dhcpcd.conf"
      - hostname: "blueberrypi"
        hw_address: "dc:a6:32:e9:53:e1"
        ip_address: "*************"
      - hostname: "octopi"
        hw_address: "dc:a6:32:0e:e2:ab"
        ip_address: "*************"
      - hostname: "status-cube-eth"
        hw_address: "b8:27:eb:1d:e8:b5"
        ip_address: "*************"
      - hostname: "status-cube-wifi"
        hw_address: "74:da:38:2b:03:22"
        ip_address: "*************"
      # IoT devices (moved from former subnet 29 to new trust network range)
      - hostname: "dreame-vacuum-valetudo"
        hw_address: "70:c9:32:0b:21:13"
        ip_address: "*************"
      - hostname: "ratgdo-door-001"
        hw_address: "c4:d8:d5:0c:d3:16"
        ip_address: "*************"
      - hostname: "ratgdo-door-002"
        hw_address: "c4:d8:d5:0b:0c:13"
        ip_address: "*************"
      - hostname: "ratgdo-door-003"
        hw_address: "c4:d8:d5:0b:08:ab"
        ip_address: "*************"
      - hostname: "myq-0a3-001"
        hw_address: "cc:6a:10:66:56:12"
        ip_address: "*************"
      - hostname: "myq-9a1-002"
        hw_address: "cc:6a:10:66:71:71"
        ip_address: "*************"
      - hostname: "myq-0bb-003"
        hw_address: "cc:6a:10:66:56:61"
        ip_address: "*************"
      - hostname: "bond-bridge-rf"
        hw_address: "3c:6a:2c:11:7d:d8"
        ip_address: "*************"
      - hostname: "hvac-colortouch-001"
        hw_address: "70:87:a7:d7:60:b4"
        ip_address: "*************"
      - hostname: "hvac-colortouch-002"
        hw_address: "70:87:a7:15:74:3e"
        ip_address: "*************"
      - hostname: "wled-florence-01"
        hw_address: "24:62:ab:fc:ff:8c"
        ip_address: "*************"

  - id: 31
    name: "piavpn"
    description: "PIA VPN Network"
    subnet: "************/24"
    subnet_mask: "*************"
    gateway: "************"
    dns_servers: "*******, *******"
    valid_lifetime: 86400  # 1 day
    max_valid_lifetime: 259200  # 3 days
    pools:
      - "************** - **************"
    reservations: []