# /etc/containers/registries.conf
[registries.search]
unqualified-search-registries = ["ghcr.io", "docker.io", 'quay.io']

[[registry]]
 location="ghcr.io"
# Optional: Add username and password if required for private repositories
# username="your_github_username"
# password="your_personal_access_token"

[[registry]]
# In Nov. 2020, Docker rate-limits image pulling.  To avoid hitting these
# limits while testing, always use the google mirror for qualified and
# unqualified `docker.io` images.
# Ref: https://cloud.google.com/container-registry/docs/pulling-cached-images
prefix="docker.io"
location="mirror.gcr.io"

# 2020-10-27 a number of images are not present in gcr.io, and podman
# barfs spectacularly when trying to fetch them. We've hand-copied
# those to quay, using skopeo copy --all ...
[[registry]]
prefix="docker.io/library"
location="quay.io/libpod"