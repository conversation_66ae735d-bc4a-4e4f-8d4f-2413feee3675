#SPDX-License-Identifier: MIT-0
---
# tasks file for podman-host

- name: Include Debian specific tasks
  ansible.builtin.include_tasks: debian.yml
  when: ansible_distribution == 'Debian'

- name: Include AlmaLinux specific tasks
  ansible.builtin.include_tasks: almalinux.yml
  when: ansible_distribution == 'AlmaLinux'

# Ansible user does not currently support subgid and subuid args.
#- name: Configure user namespaces for rootless containers
#  ansible.builtin.user:
#    name: "{{ ansible_user_id }}"
#    append: yes
#    subuid_start: 10000
#    subuid_count: 65536
#    subgid_start: 10000
#    subgid_count: 65536
