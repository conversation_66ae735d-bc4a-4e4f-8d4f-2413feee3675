# media-lidarr

An Ansible role to deploy and configure <PERSON><PERSON><PERSON> in a <PERSON>dman container.

## Requirements

- <PERSON><PERSON> installed on the target host
- NFS server accessible for media and configuration storage
- Local storage for temporary data

## Role Variables

All variables are defined in `defaults/main.yml` and can be overridden:

```yaml
# NFS configuration
media_nfs_server: "*************"
media_nfs_path: "/mnt/nasa-p1z2/Media"
media_mount_path: "/mnt/nasa-p1z2-data"
config_nfs_server: "*************"
config_nfs_path: "/mnt/nasa-p1z2/docker-data"
config_mount_path: "/mnt/nasa-p1z2-docker-data"

# Local paths
local_data_path: "/mnt/local-data"
local_docker_data_path: "/mnt/local-docker-data"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"
lidarr_image: "binhex/arch-lidarr"
lidarr_port: 8686
```

## Example Playbook

```yaml
- hosts: media_servers
  roles:
    - role: media-lidarr
      vars:
        lidarr_port: 9686  # Override default port
```

## License

MIT-0

## Author Information

Created by kriz<PERSON>
