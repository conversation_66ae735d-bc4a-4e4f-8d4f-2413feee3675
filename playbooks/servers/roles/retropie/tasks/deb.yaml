---

- name: Update DEB system
  ansible.builtin.apt:
    upgrade: yes
    update_cache: yes
    cache_valid_time: 86400 #One day

- name: Install DEB packages
  ansible.builtin.apt:
    name: "{{ DEB_pkgs }}"
    state: latest
    update_cache: yes

- name: Git checkout retropie
  ansible.builtin.git:
    repo: https://github.com/RetroPie/RetroPie-Setup.git
    depth: 1
    dest: "/home/<USER>/retropie"
  become_user: "{{ user_name }}"

#https://retropie.org.uk/forum/topic/23498/removing-retropie-from-debian
# This only runs once based on if the retropie_install.log file exists or not. After that managing retropie should be done via the GUI setup.
- name: Basic install of retropie
  ansible.builtin.shell:
    cmd: "sudo /home/<USER>/retropie/retropie_packages.sh setup basic_install >> /home/<USER>/retropie_install.log"
  args:
    chdir: "/home/<USER>/"
    creates: retropie_install.log

# ROMS go in the directory /home/<USER>/retropie/roms
# sudo mount -t cifs -o rw,user={{ user_name }},uid=1000,gid=1000 //192.168.28.31/ROMS /home/<USER>/retropie/roms
- name: Mount ROMs SMB volume
  ansible.posix.mount:
    state: mounted
    fstype: cifs
    src: //192.168.28.31/ROMS
    path: /home/<USER>/RetroPie/roms
    opts: "rw,_netdev,vers=3,file_mode=0600,dir_mode=0700,user={{ user_name }},password={{ user_name }},uid={{ user_id }},gid={{ user_id }}"
    # opts: "rw,vers=3,file_mode=0600,dir_mode=0700,username={{ user_name }},password={{ user_name }},uid=1000,gid=1000"

# How to auto login with out a DM using systemd
# https://forums.debian.net/viewtopic.php?t=143970
- name: Create auto login for tty1 service dir
  ansible.builtin.file:
    path: /etc/systemd/system/<EMAIL>.d
    state: directory
    owner: root
    group: root
    mode: '0755'

- name: Create auto login for tty1
  ansible.builtin.copy:
    dest: /etc/systemd/system/<EMAIL>.d/override.conf
    owner: root
    group: root
    mode: '0644'
    content: |
      [Service]
      Type=simple
      ExecStart=
      ExecStart=-/sbin/agetty --autologin {{ user_name }} --noclear %I 38400 linux


# # ~/.profile: executed by the command interpreter for login shells.
# # This file is not read by bash(1), if ~/.bash_profile or ~/.bash_login
# # exists.
# # see /usr/share/doc/bash/examples/startup-files for examples.
# # the files are located in the bash-doc package.

# # the default umask is set in /etc/profile; for setting the umask
# # for ssh logins, install and configure the libpam-umask package.
# #umask 022

# # if running bash
# if [ -n "$BASH_VERSION" ]; then
#     # include .bashrc if it exists
#     if [ -f "$HOME/.bashrc" ]; then                                                                                                                                                                                             . "$HOME/.bashrc"
#     fi
# fi

# # set PATH so it includes user's private bin if it exists
# if [ -d "$HOME/bin" ] ; then
#     PATH="$HOME/bin:$PATH"
# fi

# # set PATH so it includes user's private bin if it exists
# if [ -d "$HOME/.local/bin" ] ; then
#     PATH="$HOME/.local/bin:$PATH"
# fi

- name: Setup auto start emulationstation
  ansible.builtin.blockinfile:
    dest: /home/<USER>/.profile
    append_newline: true
    prepend_newline: true
    block: |
    content: |
      # Automatically start emulationstation
      if [[ -z "$DISPLAY" ]] && [[ $(tty) = /dev/tty1 ]]; then
        while true; do
          echo "INFO ==> Waiting for ROMs to be mounted"
          if mountpoint -q /home/<USER>/RetroPie/roms/; then
            break
          fi
          sleep 2
        done
        . emulationstation
        logout
      fi
