---
# Task file for setting up third-party repositories

- name: "Check if {{ repo_item.key }} repository exists"
  ansible.builtin.stat:
    path: "{{ repo_item.value.repo_file }}"
  register: repo_exists

- name: "Setup {{ repo_item.key }} repository"
  when: not repo_exists.stat.exists
  block:
    - name: "Download {{ repo_item.key }} GPG key"
      ansible.builtin.get_url:
        url: "{{ repo_item.value.key_url }}"
        dest: "/tmp/{{ repo_item.key }}.asc"
        mode: '0644'
        timeout: 30
      register: key_download
      retries: 3
      delay: 5
      until: key_download is succeeded

    - name: "Import {{ repo_item.key }} GPG key"
      ansible.builtin.shell: |
        gpg --dearmor < /tmp/{{ repo_item.key }}.asc > {{ repo_item.value.key_path }}
        chmod 644 {{ repo_item.value.key_path }}
      args:
        creates: "{{ repo_item.value.key_path }}"

    - name: "Add {{ repo_item.key }} repository"
      ansible.builtin.copy:
        content: "{{ repo_item.value.repo_line }}\n"
        dest: "{{ repo_item.value.repo_file }}"
        mode: '0644'
        owner: root
        group: root
      notify: "update apt cache"

    - name: "Clean up temporary key file"
      ansible.builtin.file:
        path: "/tmp/{{ repo_item.key }}.asc"
        state: absent

- name: "Install packages from {{ repo_item.key }} repository"
  ansible.builtin.apt:
    name: "{{ repo_item.value.packages }}"
    state: "{{ package_manager_settings.state }}"
    update_cache: true
  when: repo_item.value.packages is defined
