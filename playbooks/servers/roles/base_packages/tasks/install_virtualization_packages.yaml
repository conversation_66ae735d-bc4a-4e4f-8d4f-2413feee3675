---
# Task file for installing virtualization-specific packages

- name: "Set virtualization type fact"
  ansible.builtin.set_fact:
    virt_type: "{{ ansible_facts['virtualization_type'] | lower }}"

- name: "Install {{ virt_type }} virtualization packages"
  ansible.builtin.dnf:
    name: "{{ virtualization_packages[virt_type].packages }}"
    state: "{{ package_manager_settings.state }}"
    update_cache: false
  when: virtualization_packages[virt_type].packages is defined
  tags: ['virt_packages']

- name: "Enable and start {{ virt_type }} virtualization services"
  ansible.builtin.systemd:
    name: "{{ item }}"
    state: started
    enabled: true
  loop: "{{ virtualization_packages[virt_type].services | default([]) }}"
  when: virtualization_packages[virt_type].services is defined
  tags: ['virt_services']
