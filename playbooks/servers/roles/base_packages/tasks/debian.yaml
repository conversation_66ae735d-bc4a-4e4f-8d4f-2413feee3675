---

# Ensure sudo is installed first for fresh systems
- name: Install sudo package first (critical for init)
  ansible.builtin.apt:
    name: sudo
    state: "{{ package_manager_settings.state }}"
    update_cache: "{{ package_manager_settings.update_cache }}"
    cache_valid_time: "{{ package_manager_settings.cache_valid_time }}"
  tags: ['sudo', 'critical']

- name: Update apt cache
  ansible.builtin.apt:
    update_cache: true
    cache_valid_time: "{{ package_manager_settings.cache_valid_time }}"
  tags: ['cache']

- name: Install base Debian packages
  ansible.builtin.apt:
    name: "{{ debian_base_packages }}"
    state: "{{ package_manager_settings.state }}"
    update_cache: false  # Already updated above
  notify: "apt autoremove"
  tags: ['base_packages']

- name: Install Python packages for Debian systems
  ansible.builtin.pip:
    name: "{{ debian_pip_packages }}"
    state: "{{ package_manager_settings.state }}"
    executable: pip3
  when: debian_pip_packages | length > 0
  become: false
  tags: ['python_packages']

# Third-party repository setup
- name: Create APT keyrings directory
  ansible.builtin.file:
    path: /etc/apt/keyrings
    state: directory
    mode: '0755'
    owner: root
    group: root
  tags: ['repositories']

- name: Setup third-party repositories
  ansible.builtin.include_tasks: setup_repository.yaml
  loop: "{{ third_party_repos | dict2items }}"
  loop_control:
    loop_var: repo_item
  when: repo_item.value.enabled | default(false)
  tags: ['repositories', 'third_party']

# External tools installation
- name: Check if external tools are installed
  ansible.builtin.command: "{{ item.value.check_command }}"
  register: tool_check
  failed_when: false
  changed_when: false
  loop: "{{ external_tools | dict2items }}"
  when: item.value.enabled | default(false)
  tags: ['external_tools', 'check']

- name: Install external tools
  ansible.builtin.include_tasks: install_external_tool.yaml
  loop: "{{ external_tools | dict2items }}"
  loop_control:
    loop_var: tool_item
    index_var: tool_index
  when:
    - tool_item.value.enabled | default(false)
    - tool_check.results[tool_index].rc != 0
  tags: ['external_tools', 'install']

# Editor configuration
- name: Check if neovim is installed
  ansible.builtin.command: "{{ default_editor.editor_path }} --version"
  register: nvim_check
  failed_when: false
  changed_when: false
  when: default_editor.enabled | default(false)
  tags: ['editor']

- name: Set neovim as default editor
  community.general.alternatives:
    name: "{{ default_editor.alternatives_name }}"
    path: "{{ default_editor.editor_path }}"
  when:
    - default_editor.enabled | default(false)
    - nvim_check.rc == 0
  tags: ['editor']
