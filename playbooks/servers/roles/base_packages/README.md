# Base Packages Role

This Ansible role installs and configures essential base packages for Linux systems, supporting both Debian-based (Debian, Ubuntu) and RHEL-based (Rocky Linux, CentOS, RHEL, Fedora) distributions.

## Features

- **Multi-distribution support**: Works with Debian/Ubuntu and RHEL/Rocky/CentOS/Fedora
- **Idempotent operations**: Safe to run multiple times
- **Third-party repository management**: Automated setup of external repositories
- **External tool installation**: Support for tools installed via shell scripts
- **Virtualization-aware**: Installs appropriate guest tools based on virtualization platform
- **Configurable editor setup**: Automatically configures neovim as default editor
- **Robust error handling**: Comprehensive error checking and retry logic
- **Tag support**: Granular control over which components to install

## Requirements

- Ansible >= 2.12
- Target systems: Debian 11+, Ubuntu 20.04+, Rocky Linux 8+, CentOS 8+, RHEL 8+, Fedora 38+
- `community.general` collection >= 6.0.0
- Internet connectivity for downloading packages and external tools

## Role Variables

### Package Lists

```yaml
# Base packages for RHEL-based systems
rhel_base_packages:
  - sudo
  - epel-release
  - neovim
  - git
  # ... (see defaults/main.yml for complete list)

# Base packages for Debian-based systems
debian_base_packages:
  - sudo
  - neovim
  - git
  # ... (see defaults/main.yml for complete list)
```

### Third-party Repositories

```yaml
third_party_repos:
  eza:
    enabled: true
    key_url: "https://raw.githubusercontent.com/eza-community/eza/main/deb.asc"
    packages:
      - eza
```

### External Tools

```yaml
external_tools:
  uv:
    enabled: true
    install_script: "https://astral.sh/uv/install.sh"
    check_command: "uv --version"
```

### Package Manager Settings

```yaml
package_manager_settings:
  update_cache: true
  state: "present"  # Use 'present' for stability, 'latest' for updates
  cache_valid_time: 3600
```

## Dependencies

- `community.general` collection for the `alternatives` module

## Example Playbook

### Basic Usage

```yaml
- hosts: servers
  become: true
  roles:
    - base_packages
```

### Custom Configuration

```yaml
- hosts: servers
  become: true
  vars:
    package_manager_settings:
      state: "latest"  # Install latest versions
    external_tools:
      uv:
        enabled: false  # Disable uv installation
  roles:
    - base_packages
```

### Tag-based Execution

```yaml
# Install only base packages
- hosts: servers
  become: true
  roles:
    - role: base_packages
      tags: ['base_packages']

# Setup only third-party repositories
- hosts: servers
  become: true
  roles:
    - role: base_packages
      tags: ['repositories']
```

## Available Tags

- `info`: Display system information
- `validation`: Validate OS compatibility
- `sudo`: Install sudo package
- `critical`: Critical system packages
- `cache`: Package cache operations
- `base_packages`: Install base package lists
- `python_packages`: Install Python packages
- `repositories`: Setup third-party repositories
- `third_party`: Third-party repository packages
- `external_tools`: Install external tools
- `editor`: Configure default editor
- `virtualization`: Install virtualization-specific packages

## Supported Platforms

### Debian-based
- Debian 11 (Bullseye)
- Debian 12 (Bookworm)
- Ubuntu 20.04 LTS
- Ubuntu 22.04 LTS
- Ubuntu 24.04 LTS

### RHEL-based
- Rocky Linux 8, 9
- CentOS 8, 9
- RHEL 8, 9
- Fedora 38, 39, 40

## License

MIT

## Author Information

Created by krizzo for personal infrastructure management.
