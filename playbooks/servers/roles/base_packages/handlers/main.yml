---
# handlers file for base_packages

- name: update apt cache
  ansible.builtin.apt:
    update_cache: true
  when: ansible_facts['os_family'] | lower == 'debian'

- name: apt autoremove
  ansible.builtin.apt:
    autoremove: true
    autoclean: true
  when: ansible_facts['os_family'] | lower == 'debian'

- name: dnf autoremove
  ansible.builtin.dnf:
    autoremove: true
  when: ansible_facts['os_family'] | lower in ['redhat', 'rocky', 'centos', 'fedora']
