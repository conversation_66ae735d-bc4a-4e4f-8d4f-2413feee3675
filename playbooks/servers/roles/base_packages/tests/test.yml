---
- name: Test base_packages role
  hosts: localhost
  become: true
  gather_facts: true

  vars:
    # Test with minimal configuration
    package_manager_settings:
      state: "present"
      update_cache: true

    # Disable external tools for testing
    external_tools:
      uv:
        enabled: false

    # Disable third-party repos for testing
    third_party_repos:
      eza:
        enabled: false
      gping:
        enabled: false

  roles:
    - base_packages

  post_tasks:
    - name: Verify sudo is installed
      ansible.builtin.command: sudo --version
      changed_when: false

    - name: Verify git is installed
      ansible.builtin.command: git --version
      changed_when: false

    - name: Verify neovim is installed
      ansible.builtin.command: nvim --version
      changed_when: false
