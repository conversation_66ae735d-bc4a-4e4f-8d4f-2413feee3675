galaxy_info:
  author: krizzo
  description: Install and configure base packages for Linux systems
  company: Personal

  license: MIT

  min_ansible_version: "2.12"

  platforms:
    - name: Debian
      versions:
        - bullseye
        - bookworm
    - name: Ubuntu
      versions:
        - "20.04"
        - "22.04"
        - "24.04"
    - name: EL
      versions:
        - "8"
        - "9"
    - name: Rocky
      versions:
        - "8"
        - "9"
    - name: Fedora
      versions:
        - "38"
        - "39"
        - "40"

  galaxy_tags:
    - system
    - packages
    - base
    - setup
    - linux
    - debian
    - rhel
    - rocky
    - centos
    - fedora
    - ubuntu

dependencies:
  - name: community.general
    version: ">=6.0.0"
