
# - name: configure_docker_cd_repo_Rocky
#   ansible.builtin.get_url:
#     url: https://download.docker.com/linux/centos/docker-ce.repo
#     dest: /etc/yum.repos.d/docker-ce.repo
#     mode: 0644
#   when: ansible_facts['os_family']|lower == 'rocky'
#     or ansible_facts['os_family']|lower == 'centos'
#     or ansible_facts['os_family']|lower == 'redhat'

# - name: install_docker_ce_Rocky
#   ansible.builtin.dnf:
#     pkg:
#       - docker-ce
#       - docker-ce-cli
#     update_cache: yes
#     state: present
#   when: ansible_facts['os_family']|lower == 'rocky'
#     or ansible_facts['os_family']|lower == 'centos'
#     or ansible_facts['os_family']|lower == 'redhat'

# - name: starting_and_enabling_docker_service_RHEL
#   ansible.builtin.systemd:
#     name: docker
#     state: started
#     enabled: yes
#   when: ansible_facts['os_family']|lower == 'rocky'
#     or ansible_facts['os_family']|lower == 'centos'
#     or ansible_facts['os_family']|lower == 'redhat'
