- name: Install requirements
  ansible.builtin.apt:
    name:
      - pipx
    state: present
    update_cache: true

# These are run as the individual user
- name: Run as individual user
  block:
    - name: Install poetry
      community.general.pipx:
        name: poetry

    - name: Setup poetry path
      shell: pipx ensurepath

    # - name: Setup zsh autocomplete
    #   block:
    #     # Start poetry zsh completion setup
    #     - name: Create .zfunc directory
    #       ansible.builtin.file:
    #         path: ~/.zfunc
    #         state: directory

    #     - name: Install poetry completions
    #       ansible.builtin.shell: poetry completions zsh > ~/.zfunc/_poetry
    #       args:
    #         creates: ~/.zfunc/_poetry
    #   when: "ansible_facts.getent_passwd.{{ ansible_user_id }}[5] == '/usr/bin/zsh'"

    # - name: Setup bash autocomplete
    #   block:
    #     - name: Install poetry completions
    #       ansible.builtin.shell: poetry completions bash >> ~/.bash_completion
    #       # args: # Technically this may not create the file we'll have to check if the file exists and if it has the required infnormation already.
    #       #   creates: ~/.bash_completion
    #   when: "ansible_facts.getent_passwd.{{ ansible_user_id }}[5] == '/usr/bin/bash'"

    # Then add the following lines to your `.zshrc` just before `compinit`
    # - name: Add zsh autocomplete to path
    #   ansible.builtin.lineinfile:
    #     fpath+=~/.zfunc


    - name: Clone nautobot repo
      ansible.builtin.git:
        repo: https://github.com/nautobot/nautobot-docker-compose.git
        dest: "{{ nautobot_repo_path }}"

    # TODO Template it instead of hard code
    - name: Copy env files
      ansible.builtin.copy:
        src: "{{ item }}"
        dest: "{{ nautobot_repo_path }}/environments/{{ item }}"
        mode: '0600'
      loop:
        - local.env
        - creds.env

    - name: Copy invoke file
      ansible.builtin.copy:
        src: "{{ nautobot_repo_path }}/invoke.example.yml"
        dest: "{{ nautobot_repo_path }}/invoke.yml"
        remote_src: yes
  become: false

# - name: Enable poetry tab completion for bash
#   shell: |
#     export PATH="~/.local/bin:$PATH"
#     poetry completions bash | sudo tee -a /etc/bash_completion.d/poetry.bash-completion > /dev/null


## Setup poetry env
# poetry shell
# poetry lock
# poetry install

## Build and start the containers  This can take up to 10 mins to init the db
# invoke build start

## Invoke creating the super user after containers are running config worked to create the superuser This is not needed
# invoke createsuperuser
