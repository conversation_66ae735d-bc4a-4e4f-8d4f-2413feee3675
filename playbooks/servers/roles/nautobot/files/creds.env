NAUTOBOT_CREATE_SUPERUSER=true
NAUTOBOT_DB_PASSWORD=testing
NAUTOBOT_NAPALM_USERNAME='tester'
NAUTOBOT_NAPALM_PASSWORD='testing'
NAUTOBOT_REDIS_PASSWORD=testing
NAUTOBOT_SECRET_KEY=012345678901234567890123456789012345678901234567890123456789
NAUTOBOT_SUPERUSER_NAME=admin
NAUTOBOT_SUPERUSER_EMAIL=<EMAIL>
NAUTOBOT_SUPERUSER_PASSWORD=testing
# API token length must be exactly 40 characters
NAUTOBOT_SUPERUSER_API_TOKEN=

NAUTOBOT_CACHEOPS_REDIS=redis://:${NAUTOBOT_REDIS_PASSWORD}@redis:6379/1

# Postgres
POSTGRES_PASSWORD=${NAUTOBOT_DB_PASSWORD}
PGPASSWORD=${NAUTOBOT_DB_PASSWORD}

# MySQL

MYSQL_PASSWORD=${NAUTOBOT_DB_PASSWORD}
MYSQL_ROOT_PASSWORD=${NAUTOBOT_DB_PASSWORD}
