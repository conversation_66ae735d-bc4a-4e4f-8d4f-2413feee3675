# Nautobot Setup

[Nautobot Docker Compose Repo](https://github.com/nautobot/nautobot-docker-compose)
[Nautobot docker compose install steps](https://github.com/nautobot/nautobot-docker-compose?tab=readme-ov-file#install-docker)

## Requirements

A base linux host that has the docker service running. Here at invite we try to use debian where possible. See the [Debian Docker Host Setup](https://mswiki.invitenetworks.com/e/en/BaseSetups/Linux/Debian) wiki page for how to configure and setup a debian host that will run the docker service.

## Install dependencies

```bash
sudo apt install pipx
pipx install poetry
poetry completions bash >> ~/.bash_completion
```

## Plugins

### Install

[Plugin Install](https://github.com/nautobot/nautobot-docker-compose/blob/main/docs/plugins.md) documentation.

```bash
nb_plugins_list=(nautobot-golden-config nautobot-device-lifecycle-mgmt nautobot-device-onboarding nautobot-ssot nautobot-floor-plan)

for nbp in ${nb_plugins_list[@]}; do
	poetry add ${nbp};
done
```

### Enable and configure plugins

Edit the `./config/nautobot_config.py` file to enable the plugins. See each nautobot app (plugin) for their specific plugin configuration settings over at [Nautobot Apps Documentation](https://docs.nautobot.com/projects/core/en/stable/apps/#network-to-code-nautobot-apps)

On the apps page under the `Administrative Guide` is where you can find the plugin name for the list as well as it's configuration.

```bash
# Enable installed plugins. Add the name of each plugin to the list.
PLUGINS = ["nautobot_plugin_nornir", "nautobot_golden_config", "nautobot_ssot", "nautobot_device_lifecycle_mgmt", "nautobot_device_onboarding"]

# Plugins configuration settings. These settings are used by various plugins that the user may have installed.
# Each key in the dictionary is the name of an installed plugin and its value is a dictionary of settings.
PLUGINS_CONFIG = {
    # "nautobot_ssot": {
    #   "hide_example_jobs": True
    # },
    # "nautobot_device_onboarding": {
    #     ADD YOUR SETTINGS HERE
    # },
    "nautobot_device_lifecycle_mgmt": {
        "barchart_bar_width": float(os.environ.get("BARCHART_BAR_WIDTH", 0.1)),
        "barchart_width": int(os.environ.get("BARCHART_WIDTH", 12)),
        "barchart_height": int(os.environ.get("BARCHART_HEIGHT", 5)),
    },
    "nautobot_plugin_nornir": {
        "nornir_settings": {
            "credentials": "nautobot_plugin_nornir.plugins.credentials.env_vars.CredentialsEnvVars",
            "runner": {
                "plugin": "threaded",
                "options": {
                    "num_workers": 20,
                },
            },
        },
    },
    "nautobot_golden_config": {
        "per_feature_bar_width": 0.15,
        "per_feature_width": 13,
        "per_feature_height": 4,
        "enable_backup": True,
        "enable_compliance": True,
        "enable_intended": True,
        "enable_sotagg": True,
        "enable_plan": True,
        "enable_deploy": True,
        "enable_postprocessing": False,
        "sot_agg_transposer": None,
        "postprocessing_callables": [],
        "postprocessing_subscribed": [],
        "jinja_env": {
            "undefined": "jinja2.StrictUndefined",
            "trim_blocks": True,
            "lstrip_blocks": False,
        },
        # "default_deploy_status": "Not Approved",
        # "get_custom_compliance": "my.custom_compliance.func"
    },
}
```

TODO: Create a script that will automatically get all this information and configure the file.

```bash
# sed -Ei.bak 's/(PLUGINS = \[)(.*)(\])/\1\2'"$(printf ", \"%s\"" "${nb_plugins_list[@]}")"'\3/' ./config/nautobot_config.py
```



## Build and run the container locally

```bash
poetry shell
invoke build start
```



## END
---

Role Name
=========

A brief description of the role goes here.

Requirements
------------

Any pre-requisites that may not be covered by Ansible itself or the role should be mentioned here. For instance, if the role uses the EC2 module, it may be a good idea to mention in this section that the boto package is required.

Role Variables
--------------

A description of the settable variables for this role should go here, including any variables that are in defaults/main.yml, vars/main.yml, and any variables that can/should be set via parameters to the role. Any variables that are read from other roles and/or the global scope (ie. hostvars, group vars, etc.) should be mentioned here as well.

Dependencies
------------

A list of other roles hosted on Galaxy should go here, plus any details in regards to parameters that may need to be set for other roles, or variables that are used from other roles.

Example Playbook
----------------

Including an example of how to use your role (for instance, with variables passed in as parameters) is always nice for users too:

    - hosts: servers
      roles:
         - { role: username.rolename, x: 42 }

License
-------

BSD

Author Information
------------------

An optional section for the role authors to include contact information, or a website (HTML is not allowed).
