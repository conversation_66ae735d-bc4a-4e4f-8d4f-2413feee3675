---
# defaults file for sudoers

# Sudoers configuration
sudoers_path: "/etc/sudoers.d"
sudoers_file_mode: "0440"
sudoers_file_owner: "root"
sudoers_file_group: "root"

# Validation settings
sudoers_validate_syntax: true
sudoers_backup_files: true
sudoers_backup_suffix: ".ansible-backup"

# Default sudo permissions
sudoers_default_permission: "ALL=(ALL) NOPASSWD: ALL"
sudoers_default_requiretty: false

# Security settings
sudoers_remove_orphaned_files: true
sudoers_managed_prefix: "ansible-managed-"

# Sudoers file template settings
sudoers_use_templates: true
sudoers_template_header: true

# Common sudo rules (can be referenced in users_list)
sudoers_common_rules:
  admin: "ALL=(ALL) NOPASSWD: ALL"
  developer: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl, /usr/bin/docker, /usr/bin/git"
  operator: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl restart *, /usr/bin/systemctl status *"
  readonly: "ALL=(ALL) NOPASSWD: /usr/bin/cat /var/log/*, /usr/bin/tail /var/log/*"

# Sudoers groups configuration
sudoers_groups:
  - name: "sudo"
    rule: "%sudo ALL=(ALL:ALL) ALL"
  - name: "wheel"
    rule: "%wheel ALL=(ALL) ALL"

# Performance settings
sudoers_batch_processing: true
sudoers_validate_all_files: true
