---
# Configure sudoers for individual users

- name: Set sudoers facts for {{ user_config.name }}
  ansible.builtin.set_fact:
    sudoers_filename: "{{ sudoers_managed_prefix }}{{ user_config.name }}"
    sudoers_filepath: "{{ sudoers_path }}/{{ sudoers_managed_prefix }}{{ user_config.name }}"
    sudo_permission: "{{ user_config.sudo_permission | default(sudoers_default_permission) }}"
    sudo_rule_type: "{{ 'common' if user_config.sudo_permission in sudoers_common_rules.keys() else 'custom' }}"

- name: Resolve common sudo rule for {{ user_config.name }}
  ansible.builtin.set_fact:
    resolved_sudo_permission: "{{ sudoers_common_rules[user_config.sudo_permission] }}"
  when: sudo_rule_type == 'common'

- name: Use custom sudo rule for {{ user_config.name }}
  ansible.builtin.set_fact:
    resolved_sudo_permission: "{{ sudo_permission }}"
  when: sudo_rule_type == 'custom'

- name: Validate sudo permission format for {{ user_config.name }}
  ansible.builtin.assert:
    that:
      - resolved_sudo_permission is defined
      - resolved_sudo_permission | length > 0
      - "' ' in resolved_sudo_permission"
    fail_msg: "Invalid sudo permission format for {{ user_config.name }}: {{ resolved_sudo_permission | default('undefined') }}"
    success_msg: "Sudo permission format valid for {{ user_config.name }}"

- name: Check if sudoers file exists for {{ user_config.name }}
  ansible.builtin.stat:
    path: "{{ sudoers_filepath }}"
  register: existing_sudoers_file

- name: Backup existing sudoers file for {{ user_config.name }}
  ansible.builtin.copy:
    src: "{{ sudoers_filepath }}"
    dest: "{{ sudoers_filepath }}{{ sudoers_backup_suffix }}"
    remote_src: true
    owner: "{{ sudoers_file_owner }}"
    group: "{{ sudoers_file_group }}"
    mode: "{{ sudoers_file_mode }}"
  when: 
    - existing_sudoers_file.stat.exists
    - sudoers_backup_files | bool

- name: Create sudoers file for {{ user_config.name }}
  ansible.builtin.template:
    src: user_sudoers.j2
    dest: "{{ sudoers_filepath }}"
    owner: "{{ sudoers_file_owner }}"
    group: "{{ sudoers_file_group }}"
    mode: "{{ sudoers_file_mode }}"
    validate: "/usr/sbin/visudo -c -f %s"
    backup: "{{ sudoers_backup_files }}"
  vars:
    username: "{{ user_config.name }}"
    sudo_rule: "{{ resolved_sudo_permission }}"
    requiretty: "{{ user_config.requiretty | default(sudoers_default_requiretty) }}"
  when: sudoers_use_templates | bool
  register: sudoers_template_result

- name: Create simple sudoers file for {{ user_config.name }} (fallback)
  ansible.builtin.lineinfile:
    path: "{{ sudoers_filepath }}"
    line: "{{ user_config.name }} {{ resolved_sudo_permission }}"
    create: true
    owner: "{{ sudoers_file_owner }}"
    group: "{{ sudoers_file_group }}"
    mode: "{{ sudoers_file_mode }}"
    state: present
    validate: "/usr/sbin/visudo -c -f %s"
  when: not (sudoers_use_templates | bool)
  register: sudoers_simple_result

- name: Mark file as managed (for cleanup)
  ansible.builtin.set_fact:
    managed_sudoers_files: "{{ managed_sudoers_files | default([]) + [sudoers_filepath] }}"

- name: Display sudoers configuration summary for {{ user_config.name }}
  ansible.builtin.debug:
    msg:
      - "User: {{ user_config.name }}"
      - "File: {{ sudoers_filepath }}"
      - "Rule: {{ resolved_sudo_permission }}"
      - "Type: {{ sudo_rule_type }}"
      - "RequireTTY: {{ user_config.requiretty | default(sudoers_default_requiretty) }}"
      - "Template used: {{ sudoers_use_templates }}"
      - "Changed: {{ sudoers_template_result.changed | default(sudoers_simple_result.changed | default(false)) }}"
  when: ansible_verbosity >= 1
