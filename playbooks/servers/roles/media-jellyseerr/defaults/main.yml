---
# defaults file for media-jellyseerr

# NFS configuration
nfs_server: "192.168.28.31"
media_nfs_path: "/mnt/nasa-p1z2/Media"
media_mount_path: "/mnt/nasa-p1z2-media"
config_nfs_path: "/mnt/nasa-p1z2/docker-data"
config_mount_path: "/mnt/nasa-p1z2-docker-data"

# NFS mounts list for automated mounting
nfs_mounts:
  - name: "Media NFS volume"
    src: "{{ nfs_server }}:{{ media_nfs_path }}"
    path: "{{ media_mount_path }}"
    opts: "rw"
    fstype: "nfs"
    comment: "Main media storage mount"
  - name: "docker-data Config NFS volume"
    src: "{{ nfs_server }}:{{ config_nfs_path }}"
    path: "{{ config_mount_path }}"
    opts: "rw"
    fstype: "nfs"
    comment: "Container configuration storage mount"

# Local paths
jellyseerr_config_dir: "/mnt/docker-app-configs/jellyseerr/config"
jellyseerr_config_backups: "{{ config_mount_path }}/host-{{ ansible_hostname }}/jellyseerr/config"

# Application paths for directory creation
app_paths:
  - path: "{{ jellyseerr_config_dir }}"
    comment: "Application config local for speed"
  - path: "{{ jellyseerr_config_backups }}"
    comment: "Application config backup dir on NFS mount"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"
jellyseerr_image: "ghcr.io/fallenbagel/jellyseerr:latest"
jellyseerr_port: 5055
jellyseerr_container_name: "jellyseerr"
jellyseerr_hostname: "jellyseerr"

# Container volumes
jellyseerr_volumes:
  - "{{ jellyseerr_config_dir }}:/app/config"

# Container environment variables
jellyseerr_environment:
  LOG_LEVEL: debug
  PORT: "{{ jellyseerr_port }}"
  PUID: "{{ container_user_id }}"
  PGID: "{{ container_group_id }}"
  TZ: "America/Denver"
