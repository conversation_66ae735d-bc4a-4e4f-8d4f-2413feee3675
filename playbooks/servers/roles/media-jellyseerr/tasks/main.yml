---
# tasks file for media-jellyseerr

- name: "Mount {{ item.name }}"
  ansible.posix.mount:
    src: "{{ item.src }}"
    path: "{{ item.path }}"
    opts: "{{ item.opts }}"
    state: mounted
    fstype: "{{ item.fstype }}"
  loop: "{{ nfs_mounts }}"

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop: "{{ app_paths }}"

- name: Configure firewall rules for Jellyseerr
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ jellyseerr_port }}"
      protocol: tcp
      comment: "expose port for jellyseerr ui http"

- name: Pull the latest Jellyseerr image
  containers.podman.podman_image:
    name: "{{ jellyseerr_image }}"
  register: jellyseerr_image_pull

- name: Stop and remove existing Jellyseerr container
  containers.podman.podman_container:
    name: "{{ jellyseerr_container_name }}"
    state: absent
  when: jellyseerr_image_pull.changed

# Media Request App
- name: Start Jellyseerr container
  containers.podman.podman_container:
    name: "{{ jellyseerr_container_name }}"
    hostname: "{{ jellyseerr_hostname }}"
    image: "{{ jellyseerr_image }}"
    state: started
    restart_policy: "on-failure:5"
    network: bridge
    ports:
      - "{{ jellyseerr_port }}:{{ jellyseerr_port }}"
    volumes: "{{ jellyseerr_volumes }}"
    env: "{{ jellyseerr_environment }}"
