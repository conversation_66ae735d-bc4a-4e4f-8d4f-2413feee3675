---
# tasks file for desktop

- name: INFO ==> system info
  ansible.builtin.debug:
    msg: "OS_Family: {{ ansible_facts['os_family']|lower }}\nVirt_Role: {{ ansible_facts['virtualization_role'] }}\nVirt_Type: {{ ansible_facts['virtualization_type'] }}"
    verbosity: 1

- name: Setup DEB sudo
  import_tasks: deb.yaml
  when: ansible_facts['os_family']|lower == 'debian'

- name: Setup RHEL sudo
  import_tasks: rhel.yaml
  when: ansible_facts['os_family']|lower == 'rocky'
    or ansible_facts['os_family']|lower == 'centos'
    or ansible_facts['os_family']|lower == 'redhat'
