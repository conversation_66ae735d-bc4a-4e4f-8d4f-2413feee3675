#!/bin/bash
# DHCP Configuration Validation Script
# This script validates the DHCP playbook configuration and structure

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "=== DHCP Configuration Validation ==="
echo "Working directory: $SCRIPT_DIR"
echo

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}[OK]${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}[WARN]${NC} $message"
    else
        echo -e "${RED}[ERROR]${NC} $message"
    fi
}

# Check if required files exist
echo "Checking file structure..."

required_files=(
    "dhcpServers.yaml"
    "test-vault-vars.yaml"
    "debug-vault.sh"
    "recreate-vault.sh"
    "hosts.yaml"
    "group_vars/dhcpServers.yml"
    "group_vars/dhcpServers_vault.yml.example"
    "host_vars/silmarillion-001.yml"
    "host_vars/silmarillion-002.yml"
    "roles/dhcp-kea/defaults/main.yml"
    "roles/dhcp-kea/tasks/main.yml"
    "roles/dhcp-kea/templates/subnet.conf.json.j2"
    "roles/dhcp-kea/templates/subnet-reservations.conf.json.j2"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "OK" "Found: $file"
    else
        print_status "ERROR" "Missing: $file"
    fi
done

echo

# Check if vault file exists (encrypted or example)
echo "Checking vault configuration..."
if [ -f "group_vars/dhcpServers_vault.yml" ]; then
    if grep -q "ANSIBLE_VAULT" "group_vars/dhcpServers_vault.yml"; then
        print_status "OK" "Vault file is encrypted"
    else
        print_status "WARN" "Vault file exists but is not encrypted"

        # Check if default password is still being used
        if grep -q "CHANGE_ME_BEFORE_ENCRYPTING" "group_vars/dhcpServers_vault.yml"; then
            print_status "ERROR" "Default password found in vault file - please change before encrypting"
        fi

        if grep -q "testing" "group_vars/dhcpServers_vault.yml"; then
            print_status "WARN" "Test password found in vault file - consider using a stronger password"
        fi
    fi
else
    print_status "WARN" "Vault file not found - copy from example and encrypt"
fi

echo

# Check YAML syntax (if ansible is available)
echo "Checking YAML syntax..."
if command -v ansible-playbook &> /dev/null; then
    if ansible-playbook dhcpServers.yaml --syntax-check &> /dev/null; then
        print_status "OK" "Playbook syntax is valid"
    else
        print_status "ERROR" "Playbook syntax check failed"
        ansible-playbook dhcpServers.yaml --syntax-check
    fi
else
    print_status "WARN" "Ansible not found - skipping syntax check"
fi

echo

# Check inventory structure
echo "Checking inventory structure..."
if grep -q "dhcpServers:" hosts.yaml; then
    print_status "OK" "dhcpServers group found in inventory"
    
    # Check if hosts are defined
    if grep -A 10 "dhcpServers:" hosts.yaml | grep -q "silmarillion-001"; then
        print_status "OK" "Primary server (silmarillion-001) found in inventory"
    else
        print_status "ERROR" "Primary server not found in dhcpServers group"
    fi
    
    if grep -A 10 "dhcpServers:" hosts.yaml | grep -q "silmarillion-002"; then
        print_status "OK" "Secondary server (silmarillion-002) found in inventory"
    else
        print_status "ERROR" "Secondary server not found in dhcpServers group"
    fi
else
    print_status "ERROR" "dhcpServers group not found in inventory"
fi

echo

# Check role structure
echo "Checking role structure..."
role_files=(
    "roles/dhcp-kea/defaults/main.yml"
    "roles/dhcp-kea/tasks/main.yml"
    "roles/dhcp-kea/handlers/main.yml"
    "roles/dhcp-kea/templates/subnet.conf.json.j2"
    "roles/dhcp-kea/templates/subnet-reservations.conf.json.j2"
    "roles/dhcp-kea/README.md"
)

for file in "${role_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "OK" "Role file: $file"
    else
        print_status "ERROR" "Missing role file: $file"
    fi
done

echo

# Check for old template files (should be removed)
echo "Checking for old template files..."
old_templates=(
    "roles/dhcp-kea/templates/subnet-24-guest.conf.json.j2"
    "roles/dhcp-kea/templates/subnet-29-iot.conf.json.j2"
)

old_files_found=false
for file in "${old_templates[@]}"; do
    if [ -f "$file" ]; then
        print_status "WARN" "Old template file found (should be removed): $file"
        old_files_found=true
    fi
done

if [ "$old_files_found" = false ]; then
    print_status "OK" "No old template files found"
fi

echo

# Summary
echo "=== Validation Summary ==="
echo "Configuration structure appears to be set up correctly."
echo
echo "Next steps:"
echo "1. Set up vault file (choose one method):"
echo "   Method A - Use recreation script:"
echo "     ./recreate-vault.sh"
echo "   Method B - Manual setup:"
echo "     cp group_vars/dhcpServers_vault.yml.example group_vars/dhcpServers_vault.yml"
echo "     # Edit with your passwords"
echo "     ansible-vault encrypt group_vars/dhcpServers_vault.yml"
echo
echo "2. Test vault variable access:"
echo "   ./debug-vault.sh"
echo
echo "3. Test vault variable mapping:"
echo "   ansible-playbook test-vault-vars.yaml --ask-vault-pass"
echo
echo "4. Test deployment:"
echo "   ansible-playbook dhcpServers.yaml --check --ask-vault-pass"
echo
echo "5. Deploy to servers:"
echo "   ansible-playbook dhcpServers.yaml --ask-vault-pass"
echo

print_status "OK" "Validation complete"
