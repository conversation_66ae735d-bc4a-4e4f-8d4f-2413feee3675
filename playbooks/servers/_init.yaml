---

# This playbook is used to
# Rocky Linux ks.cfg file for init setup
# Debian Use pre-seed conf file for init setup

# TODO: Create a PXE setup so base installs are automated.

# Install some basic packages and setup users on a fresh install of linux

# Run:
#   CWD: ansible/playbooks/servers/
#   ansible-playbook -e "hosts=testServers" _init.yaml -kK -v
#   ansible-playbook -l testServers _init.yaml -kK -v
#   ansible-playbook -e "hosts=prodServers" _init.yaml -kK -v

- name: Init fresh linux install
  hosts: "{{ hosts | default('testServers') }}"
  gather_facts: true
  become: true
  become_method: su
  become_user: root

  vars_files:
    - ./global_vars/users.yaml

  tasks:
    - name: Install default packages
      ansible.builtin.import_role:
        name: base_packages

    - name: Configure users
      ansible.builtin.import_role:
        name: users

    - name: Configure sudoers
      ansible.builtin.import_role:
        name: sudoers
