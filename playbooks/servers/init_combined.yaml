---

# Combined Init Playbook
# This playbook combines the functionality of both _init.yaml and base.yaml
# It handles initial setup on fresh systems where sudo may not be available
# and uses su to become root for initial configuration.

# Prerequisites:
# - SSH access to target host as user "aule" with password "changeme" (or vault_init_aule_password)
# - Root access via su with password "changeme" (or vault_init_root_password)
# - Vault file group_vars/init_vault.yml with new passwords (encrypted with ansible-vault)

# Setup vault file:
#   cp group_vars/init_vault.yml.example group_vars/init_vault.yml
#   # Edit init_vault.yml with your secure passwords
#   ansible-vault encrypt group_vars/init_vault.yml

# Run:
#   CWD: ansible/playbooks/servers/
#
#   # With encrypted vault file (production):
#   ansible-playbook -e "hosts=testServers" init_combined.yaml -k -K --ask-vault-pass -v
#
#   # With unencrypted vault file (testing):
#   ansible-playbook -e "hosts=testServers" init_combined.yaml -k -K -v

- name: Combined Init - Fresh Linux Install with Base Setup
  hosts: "{{ hosts | default('testServers') }}"
  gather_facts: false  # Disable initially - fresh systems may not have Python
  remote_user: aule
  become: true
  become_method: su
  become_user: root
  
  vars:
    # Override default connection settings for initial setup
    ansible_user: aule
    ansible_ssh_pass: "{{ vault_init_aule_password | default('changeme') }}"
    ansible_become_pass: "{{ vault_init_root_password | default('changeme') }}"

  vars_files:
    - ./global_vars/users.yaml
    # Vault file is optional for testing - can be unencrypted or encrypted
    - ./group_vars/init_vault.yml

  pre_tasks:
    - name: Display connection information
      ansible.builtin.debug:
        msg: |
          Connecting to {{ inventory_hostname }} as user {{ ansible_user }}
          Using su to become root for initial setup
          This playbook will install base packages, configure users, and setup sudo access
          Note: Python will be installed if missing, then facts will be gathered
      tags: ['info']

    - name: Test initial connectivity and root access
      ansible.builtin.ping:
      tags: ['connectivity']

    - name: Bootstrap Python installation (raw commands for fresh systems)
      block:
        - name: Check if Python3 is available
          ansible.builtin.raw: command -v python3
          register: python3_check
          ignore_errors: true
          changed_when: false
          tags: ['bootstrap', 'python']

        - name: Install Python3 on Debian/Ubuntu systems
          ansible.builtin.raw: |
            apt-get update && apt-get install -y python3 python3-apt
          when: python3_check.rc != 0
          register: python_install_deb
          ignore_errors: true
          tags: ['bootstrap', 'python']

        - name: Install Python3 on RHEL/Rocky/CentOS systems
          ansible.builtin.raw: |
            if command -v dnf >/dev/null 2>&1; then
              dnf install -y python3
            elif command -v yum >/dev/null 2>&1; then
              yum install -y python3
            else
              echo "No package manager found"
              exit 1
            fi
          when:
            - python3_check.rc != 0
            - python_install_deb is skipped or python_install_deb.rc != 0
          register: python_install_rhel
          ignore_errors: true
          tags: ['bootstrap', 'python']

        - name: Verify Python3 installation
          ansible.builtin.raw: python3 --version
          register: python_version_check
          changed_when: false
          tags: ['bootstrap', 'python']

        - name: Display Python installation result
          ansible.builtin.debug:
            msg: |
              Python3 status: {{ 'Already installed' if python3_check.rc == 0 else 'Newly installed' }}
              Python3 version: {{ python_version_check.stdout.strip() }}
          tags: ['bootstrap', 'python']

      rescue:
        - name: Python installation failed
          ansible.builtin.fail:
            msg: |
              Failed to install Python3 on the target system.
              This is required for Ansible to function properly.
              Please manually install Python3 and try again.

              For Debian/Ubuntu: apt-get update && apt-get install -y python3
              For RHEL/Rocky/CentOS: dnf install -y python3 (or yum install -y python3)

    - name: Gather system facts (now that Python is available)
      ansible.builtin.setup:
      tags: ['facts']

    - name: Display system information
      ansible.builtin.debug:
        msg: |
          OS Family: {{ ansible_facts['os_family'] }}
          Distribution: {{ ansible_facts['distribution'] }} {{ ansible_facts['distribution_version'] }}
          Architecture: {{ ansible_facts['architecture'] }}
          Virtualization: {{ ansible_facts['virtualization_type'] | default('physical') }}
          Python Version: {{ ansible_facts['python_version'] }}
      tags: ['info']

  tasks:
    - name: Phase 1 - Install base packages (including sudo)
      block:
        - name: Install default packages
          ansible.builtin.import_role:
            name: base_packages
          tags: ['packages']

        - name: Verify sudo is installed and functional
          ansible.builtin.command: which sudo
          register: sudo_check
          failed_when: sudo_check.rc != 0
          tags: ['packages', 'verification']

        - name: Test sudo functionality
          ansible.builtin.command: sudo -V
          register: sudo_version
          tags: ['packages', 'verification']

        - name: Display sudo version
          ansible.builtin.debug:
            msg: "Sudo version: {{ sudo_version.stdout_lines[0] }}"
          tags: ['packages', 'verification']

      rescue:
        - name: Package installation failed
          ansible.builtin.fail:
            msg: |
              Failed to install base packages or sudo is not functional.
              Please check the base_packages role configuration.
              Error: {{ ansible_failed_result.msg | default('Unknown error') }}

    - name: Phase 2 - Configure users and groups
      block:
        - name: Configure users
          ansible.builtin.import_role:
            name: users
          tags: ['users']

        - name: Verify user creation
          ansible.builtin.getent:
            database: passwd
            key: "{{ item.name }}"
          loop: "{{ users_list }}"
          loop_control:
            label: "{{ item.name }}"
          register: user_verification
          tags: ['users', 'verification']

        - name: Display created users
          ansible.builtin.debug:
            msg: "User {{ item.item.name }} created with UID {{ item.ansible_facts.getent_passwd[item.item.name][1] }}"
          loop: "{{ user_verification.results }}"
          loop_control:
            label: "{{ item.item.name }}"
          when: item.ansible_facts.getent_passwd[item.item.name] is defined
          tags: ['users', 'verification']

      rescue:
        - name: User configuration failed
          ansible.builtin.fail:
            msg: |
              Failed to configure users.
              Please check the users role configuration and global_vars/users.yaml.
              Error: {{ ansible_failed_result.msg | default('Unknown error') }}

    - name: Phase 3 - Configure sudo access
      block:
        - name: Configure sudoers
          ansible.builtin.import_role:
            name: sudoers
          tags: ['sudoers']

        - name: Verify sudo configuration for admin users
          ansible.builtin.command: sudo -l -U {{ item.name }}
          loop: "{{ users_list | selectattr('sudoer', 'defined') | selectattr('sudoer', 'equalto', true) | list }}"
          loop_control:
            label: "{{ item.name }}"
          register: sudo_verification
          ignore_errors: true
          tags: ['sudoers', 'verification']

        - name: Display sudo verification results
          ansible.builtin.debug:
            msg: |
              User: {{ item.item.name }}
              Sudo privileges: {{ 'Configured' if item.rc == 0 else 'Failed' }}
          loop: "{{ sudo_verification.results }}"
          loop_control:
            label: "{{ item.item.name }}"
          tags: ['sudoers', 'verification']

      rescue:
        - name: Sudoers configuration failed
          ansible.builtin.fail:
            msg: |
              Failed to configure sudoers.
              Please check the sudoers role configuration.
              Error: {{ ansible_failed_result.msg | default('Unknown error') }}

    - name: Phase 4 - Password Rotation and Security Hardening
      block:
        - name: Rotate aule user password
          ansible.builtin.user:
            name: aule
            password: "{{ vault_new_aule_password | password_hash('sha512') }}"
            update_password: always
          when: vault_new_aule_password is defined
          tags: ['passwords', 'security']

        - name: Rotate root password
          ansible.builtin.user:
            name: root
            password: "{{ vault_new_root_password | password_hash('sha512') }}"
            update_password: always
          when: vault_new_root_password is defined
          tags: ['passwords', 'security']

        - name: Set passwords for created users
          ansible.builtin.user:
            name: "{{ item.name }}"
            password: "{{ vault_user_passwords[item.name] | password_hash('sha512') }}"
            update_password: always
          loop: "{{ users_list }}"
          loop_control:
            label: "{{ item.name }}"
          when:
            - vault_user_passwords is defined
            - vault_user_passwords[item.name] is defined
          tags: ['passwords', 'users']

        - name: Force password change on first login for new users
          ansible.builtin.command: chage -d 0 {{ item.name }}
          loop: "{{ users_list }}"
          loop_control:
            label: "{{ item.name }}"
          when:
            - vault_password_policy is defined
            - vault_password_policy.force_password_change | default(false)
            - item.name != 'krizzo'  # Don't force password change for main admin
          ignore_errors: true
          tags: ['passwords', 'security']

        - name: Configure password expiration policy
          ansible.builtin.command: >
            chage -M {{ vault_password_policy.password_expire_days | default(90) }}
                  -m {{ vault_password_policy.password_min_age | default(1) }}
                  {{ item.name }}
          loop: "{{ users_list }}"
          loop_control:
            label: "{{ item.name }}"
          when: vault_password_policy is defined
          ignore_errors: true
          tags: ['passwords', 'security']

        - name: Verify password changes
          ansible.builtin.debug:
            msg: "Password rotation completed for {{ users_list | length }} users plus aule and root"
          tags: ['passwords', 'verification']

      rescue:
        - name: Password rotation failed
          ansible.builtin.debug:
            msg: |
              Password rotation failed. This is not critical for initial setup.
              You can manually change passwords later.
              Error: {{ ansible_failed_result.msg | default('Unknown error') }}
          tags: ['passwords']

  post_tasks:
    - name: Final verification and next steps
      block:
        - name: Test SSH key authentication for krizzo user
          ansible.builtin.command: sudo -u krizzo ssh-keygen -l -f /home/<USER>/.ssh/authorized_keys
          register: ssh_key_check
          ignore_errors: true
          when: users_list | selectattr('name', 'equalto', 'krizzo') | selectattr('ssh', 'equalto', true) | list | length > 0
          tags: ['verification', 'ssh']

        - name: Display SSH key status
          ansible.builtin.debug:
            msg: |
              SSH keys for krizzo: {{ 'Configured' if ssh_key_check.rc == 0 else 'Not configured or failed' }}
          when: ssh_key_check is defined
          tags: ['verification', 'ssh']

        - name: Display completion summary
          ansible.builtin.debug:
            msg: |
              ========================================
              INIT COMBINED PLAYBOOK COMPLETED
              ========================================

              ✓ Base packages installed (including sudo)
              ✓ Users configured from global_vars/users.yaml
              ✓ Sudo access configured for admin users
              ✓ Passwords rotated from vault (aule, root, and user accounts)
              ✓ Security policies applied

              SECURITY NOTICE:
              - Initial passwords (changeme/changeme) have been changed
              - New passwords are set from vault_new_aule_password and vault_new_root_password
              - User account passwords are set from vault_user_passwords

              NEXT STEPS:
              1. Test SSH key authentication:
                 ssh krizzo@{{ inventory_hostname }}

              2. Test new aule password:
                 ssh aule@{{ inventory_hostname }}
                 (Use vault_new_aule_password)

              3. For subsequent runs, you can use:
                 ansible-playbook -e "hosts={{ group_names[0] | default('testServers') }}" base.yaml

              4. The system is now ready for normal Ansible operations using sudo

              5. Consider disabling password authentication and enabling key-only SSH

              ========================================
          tags: ['info']

      rescue:
        - name: Final verification failed
          ansible.builtin.debug:
            msg: |
              Some verification steps failed, but the main configuration should be complete.
              Please manually verify SSH access and sudo functionality.

  handlers:
    - name: restart ssh
      ansible.builtin.service:
        name: "{{ 'ssh' if ansible_facts['os_family'] == 'Debian' else 'sshd' }}"
        state: restarted
      listen: "restart ssh service"
