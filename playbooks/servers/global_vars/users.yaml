---
# Global Users Configuration
# This file defines all users to be managed across the infrastructure
# Follows optimized users and sudoers role structure

users_list:
  # Primary Administrator - krizzo
  - name: "krizzo"
    id: "1985"
    shell: "/usr/bin/zsh"
    create_home: true
    ssh: true
    ssh_pub_keys:
      - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAtrnC0RHMFtRV5x6hP1ttAyNLQdengHzk1hu0WZUl/U krizzo@krizzo-linux-vm-personal"
      - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEUmbXta2ZkI/xA8QXuPpHeaU1ROFd8i+/J7b2ywOYlF <EMAIL>"
      - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAICTccf5ECJOkfMIlY9jLPqXXOC9yRnmI41xMUJwa0KXD krizzo@krizzo-purestorage-laptop-work"
      - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEetWgrmXV9UvIBaJ4JP/SnBsRjNVkytAkHsjvSya+BP krizzo@krizzo-purestorage-laptop-personal"
    sudoer: true
    sudo_permission: "admin"  # Uses common rule for full admin access
    groups: ["sudo", "docker", "users"]
    additional_groups: ["adm", "systemd-journal"]
    environment:
      EDITOR: "vim"
      LANG: "en_US.UTF-8"
      HISTSIZE: "10000"
      HISTFILESIZE: "20000"
    create_directories: true
    requiretty: false
#  # Family Member - srizzo
#  - name: "srizzo"
#    id: "1988"
#    shell: "/usr/bin/zsh"
#    create_home: true
#    ssh: false  # No SSH access needed
#    sudoer: true
#    sudo_permission: "admin"  # Full admin access
#    groups: ["sudo", "users"]
#    environment:
#      EDITOR: "nano"  # Prefer nano for simplicity
#      LANG: "en_US.UTF-8"
#    create_directories: true
#    requiretty: true  # Require TTY for security
#  # Family Member - lrizzo (Limited Access)
#  - name: "lrizzo"
#    id: "2012"
#    shell: "/usr/bin/zsh"
#    create_home: true
#    ssh: true
#    ssh_pub_keys:
#      - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIBTwvt6RjBMvi6GrP1R5GM6AW/mbHaKd0uZ9L8elJZe/ lrizzo-desktop\\lilli@lrizzo-desktop-personal"
#    sudoer: false  # No sudo access
#    groups: ["users"]
#    environment:
#      EDITOR: "nano"
#      LANG: "en_US.UTF-8"
#    create_directories: true
#  # Family Member - trizzo (Standard User)
#  - name: "trizzo"
#    id: "2015"
#    shell: "/usr/bin/zsh"
#    create_home: true
#    ssh: false  # No SSH access needed
#    sudoer: false  # No sudo access
#    groups: ["users"]
#    environment:
#      EDITOR: "nano"
#      LANG: "en_US.UTF-8"
#    create_directories: true
#  # Family Member - frizzo (Standard User)
#  - name: "frizzo"
#    id: "2018"
#    shell: "/usr/bin/zsh"
#    create_home: true
#    ssh: false  # No SSH access needed
#    sudoer: false  # No sudo access
#    groups: ["users"]
#    environment:
#      EDITOR: "nano"
#      LANG: "en_US.UTF-8"
#    create_directories: true

# User Management Configuration
# These settings apply to all users and complement the role defaults

# Global user settings
users_global_settings:
  # Default shell configurations
  default_shell: "/usr/bin/zsh"
  fallback_shell: "/bin/bash"

  # SSH configuration
  ssh_key_type: "ed25519"
  ssh_dir_permissions: "0700"
  authorized_keys_permissions: "0600"

  # Environment defaults
  default_environment:
    HISTCONTROL: "ignoreboth"
    HISTTIMEFORMAT: "%F %T "
    TERM: "xterm-256color"

  # Security settings
  password_policy:
    min_length: 12
    require_special_chars: true
#    expire_days: 90

  # Directory creation
  standard_directories:
    - "bin"
    - ".local"
    - ".local/bin"
    - ".config"
    - "Documents"
    - "Downloads"

# Sudo configuration templates
# These complement the sudoers role common rules
users_sudo_templates:
  family_admin:
    rule: "ALL=(ALL) NOPASSWD: ALL"
    description: "Full administrative access for family members"
    requiretty: true

  developer:
    rule: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl, /usr/bin/docker, /usr/bin/git, /usr/bin/npm, /usr/bin/yarn"
    description: "Development tools access"
    requiretty: false

  media_admin:
    rule: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl restart *media*, /usr/bin/systemctl status *media*"
    description: "Media server management"
    requiretty: false

# Group definitions
# These groups should exist on target systems
users_required_groups:
  - name: "users"
    gid: "100"
    description: "Standard users group"

  - name: "sudo"
    gid: "27"
    description: "Sudo access group"

  - name: "docker"
    gid: "999"
    description: "Docker access group"

  - name: "media"
    gid: "2000"
    description: "Media server access group"
