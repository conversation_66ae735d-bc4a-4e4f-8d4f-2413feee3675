# Media Miner Playbook

This playbook sets up a complete media server with various components for managing and processing media files.

## Components

- **Lidarr**: Music management
- **Sonarr**: TV show management
- **Radarr**: Movie management
- **Readarr**: Ebook management
- **Bazarr**: Subtitle management
- **Prowlarr**: Indexer management
- **NZBGet**: Download client
- **Encoding**: Media transcoding (Tdarr/Unmanic/FileFlows)

## Requirements

- Ansible 2.9+
- Target host running Debian or AlmaLinux
- NFS server for media storage
- Sufficient disk space for media processing

## Usage

Run the playbook with:

```bash
# Run on default host (moria)
ansible-playbook playbooks/servers/media_miner.yaml

# Run on a specific host
ansible-playbook -e "hosts=mediaserver01" playbooks/servers/media_miner.yaml

# Run only specific components using tags
ansible-playbook playbooks/servers/media_miner.yaml --tags "setup,movies,tv"
```

## Configuration

Edit `vars/media_miner.yml` to customize the deployment:

- Enable/disable optional components
- Change NFS server settings
- Modify container ports
- Select encoding application

## Maintenance

- To update all containers: `ansible-playbook playbooks/servers/media_miner.yaml --tags "container"`
- To check service status: `podman ps` on the target host
