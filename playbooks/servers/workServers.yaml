---

# Used to update and true up system base configuration such as users, applications, etc...

# Run:
#   CWD: ansible/playbooks/linux/
#   ansible-playbook -e "hosts=testServers" base_setup.yaml

# Set a raspberry pi up using the lite image with custom settings.
# Change the hostname
#  sudo sed -i 's/silmarillion-004/blueberrypi/g' /etc/hosts; sudo hostnamectl set-hostname blueberrypi

- name: Init fresh linux install
  hosts: "{{ hosts | default('workServers') }}"
  gather_facts: true
  become: true

  vars:
    users_list:
      - groups: ''
        id: '1985'
        name: krizzo
        shell: /usr/bin/zsh
        ssh: true
        ssh_pub_keys:
          - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEUmbXta2ZkI/xA8QXuPpHeaU1ROFd8i+/J7b2ywOYlF <EMAIL>
          - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAICTccf5ECJOkfMIlY9jLPqXXOC9yRnmI41xMUJwa0KXD krizzo@krizzo-purestorage-laptop-work
          - ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEetWgrmXV9UvIBaJ4JP/SnBsRjNVkytAkHsjvSya+BP krizzo@krizzo-purestorage-laptop-personal
        sudo_permission: 'ALL=(ALL) NOPASSWD: ALL'
        sudoer: true

  tasks:
    - name: Install default packages
      ansible.builtin.import_role:
        name: base_packages

    - name: Configure users
      ansible.builtin.import_role:
        name: users

    - name: Configure sudoers
      ansible.builtin.import_role:
        name: sudoers
