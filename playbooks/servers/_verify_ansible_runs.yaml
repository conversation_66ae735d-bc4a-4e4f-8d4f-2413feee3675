---

# Run:
#   CWD: ansible/playbooks/servers/
#   ansible-playbook -e "hosts=dhcpTestServers" _verify_ansible_runs.yaml
#   ansible-playbook -l dhcpTestServers _verify_ansible_runs.yaml

- name: Testing ansible connectivity to servers
  hosts: "{{ hosts | default('testServers') }}"
  gather_facts: true
  become: true

  tasks:
    - name: Facts gathered
      debug:
        msg: "{{ hostvars[inventory_hostname] | to_nice_json }}"

    - name: Network Info
      debug:
        msg: "Int: {{ ansible_default_ipv4.interface }}\nIP : {{ ansible_default_ipv4.address }}"