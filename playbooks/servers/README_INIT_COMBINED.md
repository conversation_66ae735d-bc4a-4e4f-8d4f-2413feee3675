# Combined Init Playbook

## Overview

The `init_combined.yaml` playbook combines the functionality of both `_init.yaml` and `base.yaml` playbooks. It's designed to handle initial setup on fresh Linux systems where sudo may not be available and uses `su` to become root for initial configuration.

## Prerequisites

- SSH access to target host as user "aule" with password "changeme"
- Root access via `su` with password "changeme"
- Target host should be a fresh Linux installation (Debian/Ubuntu or RHEL/Rocky/CentOS)
- Ansible vault file configured with new passwords (see Setup section below)

## What This Playbook Does

**Bootstrap Phase - Python Installation**
   - Tests connectivity and root access via su
   - Automatically detects and installs Python3 if missing (required for Ansible)
   - Works on fresh systems without Python pre-installed
   - Gathers system facts once Python is available

1. **Phase 1 - Base Package Installation**
   - Installs essential packages including sudo (prioritized)
   - Configures additional repositories (eza, gping)
   - Installs development tools (neovim, git, etc.)
   - Verifies sudo installation and functionality

2. **Phase 2 - User Management**
   - Creates users defined in `global_vars/users.yaml`
   - Configures SSH keys for users with SSH access
   - Sets up user environments and shells
   - Creates necessary directories

3. **Phase 3 - Sudo Configuration**
   - Configures sudoers rules for admin users
   - Sets up group-based sudo access
   - Validates sudo configuration

4. **Phase 4 - Password Rotation and Security**
   - Changes aule user password from "changeme" to vault-defined secure password
   - Changes root password from "changeme" to vault-defined secure password
   - Sets secure passwords for all created user accounts
   - Configures password expiration policies
   - Applies security hardening settings

## Setup

### 1. Configure Vault File

Before running the playbook, you need to set up the vault file with secure passwords:

```bash
# Navigate to the servers directory
cd ansible/playbooks/servers/

# Run the setup script (recommended)
./setup-init-vault.sh

# OR manually create the vault file
cp group_vars/init_vault.yml.example group_vars/init_vault.yml
# Edit init_vault.yml with your secure passwords
ansible-vault encrypt group_vars/init_vault.yml
```

The setup script will:
- Guide you through setting secure passwords
- Generate strong passwords if you prefer
- Encrypt the vault file automatically
- Validate the setup

## Usage

### Basic Usage

```bash
# Navigate to the servers directory
cd ansible/playbooks/servers/

# For TESTING with unencrypted vault file:
ansible-playbook -e "hosts=testServers" init_combined.yaml -k -K -v

# For PRODUCTION with encrypted vault file:
ansible-playbook -e "hosts=testServers" init_combined.yaml -k -K --ask-vault-pass -v

# Run against specific host (testing):
ansible-playbook -l hostname init_combined.yaml -k -K -v

# Run against specific host (production):
ansible-playbook -l hostname init_combined.yaml -k -K --ask-vault-pass -v
```

### Command Line Options Explained

- `-k` : Prompt for SSH password (for user "aule")
- `-K` : Prompt for privilege escalation password (root password for `su`)
- `--ask-vault-pass` : Prompt for vault password to decrypt init_vault.yml (only needed if encrypted)
- `-v` : Verbose output
- `-e "hosts=groupname"` : Target specific host group
- `-l hostname` : Limit to specific host

### Example Runs

**Testing (unencrypted vault):**
```bash
ansible-playbook -e "hosts=testServers" init_combined.yaml -k -K -v
```

**Production (encrypted vault):**
```bash
ansible-playbook -e "hosts=testServers" init_combined.yaml -k -K --ask-vault-pass -v
```

When prompted:
- SSH password: `changeme` (for user aule)
- BECOME password: `changeme` (for su to root)
- Vault password: `[your vault encryption password]` (only if using encrypted vault)

## Configuration

### User Configuration

Users are defined in `global_vars/users.yaml`. The playbook will create all users listed in the `users_list` variable.

### Package Configuration

Packages are defined in the `base_packages` role:
- Debian packages: `roles/base_packages/defaults/main.yml` (DEB_pkgs)
- RHEL packages: `roles/base_packages/defaults/main.yml` (RHEL_pkgs)

## Post-Execution

After successful completion:

1. **Test SSH Key Authentication**
   ```bash
   ssh krizzo@hostname
   ```

2. **Subsequent Runs**
   For future configuration updates, you can use the regular `base.yaml` playbook:
   ```bash
   ansible-playbook -e "hosts=testServers" base.yaml
   ```

3. **Verify Sudo Access**
   ```bash
   ssh krizzo@hostname
   sudo whoami
   ```

## Troubleshooting

### Common Issues

1. **Python Not Found / Fact Gathering Failed**
   - The playbook automatically installs Python3 if missing
   - If bootstrap fails, manually install Python3:
     - Debian/Ubuntu: `apt-get update && apt-get install -y python3`
     - RHEL/Rocky/CentOS: `dnf install -y python3` or `yum install -y python3`
   - Check that the target system has a working package manager

2. **SSH Connection Failed**
   - Verify the target host is accessible
   - Ensure user "aule" exists with password "changeme"
   - Check SSH service is running

3. **Su Authentication Failed**
   - Verify root password is correct ("changeme")
   - Ensure su is available on the target system

4. **Temporary Directory Creation Failed**
   - The playbook now uses `/tmp/ansible_runs` for temporary files
   - Ensure `/tmp` is writable on the target system
   - Check disk space availability

5. **Package Installation Failed**
   - Check internet connectivity on target host
   - Verify package repositories are accessible
   - Review package names in defaults/main.yml

6. **User Creation Failed**
   - Check `global_vars/users.yaml` syntax
   - Verify user IDs don't conflict with existing users
   - Ensure required groups exist

### Debug Mode

Run with extra verbosity for debugging:
```bash
ansible-playbook -e "hosts=testServers" init_combined.yaml -k -K -vvv
```

### Tags

You can run specific phases using tags:
```bash
# Only install packages
ansible-playbook -e "hosts=testServers" init_combined.yaml -k -K --tags packages

# Only configure users
ansible-playbook -e "hosts=testServers" init_combined.yaml -k -K --tags users

# Only configure sudoers
ansible-playbook -e "hosts=testServers" init_combined.yaml -k -K --tags sudoers
```

## Security Considerations

- The playbook uses hardcoded credentials for initial setup only
- After completion, password-based authentication should be disabled
- SSH key authentication is configured for secure access
- Sudo access is properly configured with appropriate restrictions

## Next Steps

After running this playbook:

1. Disable password authentication in SSH configuration
2. Test all user accounts and sudo access
3. Configure additional services as needed
4. Use the regular `base.yaml` playbook for future updates

## Related Files

- `_init.yaml` - Original init playbook (uses su)
- `base.yaml` - Original base playbook (uses sudo)
- `global_vars/users.yaml` - User configuration
- `roles/base_packages/` - Package installation role
- `roles/users/` - User management role
- `roles/sudoers/` - Sudo configuration role
