#!/bin/bash
# Script to recreate the vault file with proper variables

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "=== DHCP Vault Recreation Script ==="
echo

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}[OK]${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}[WARN]${NC} $message"
    else
        echo -e "${RED}[ERROR]${NC} $message"
    fi
}

# Backup existing vault file if it exists
if [ -f "group_vars/dhcpServers_vault.yml" ]; then
    backup_file="group_vars/dhcpServers_vault.yml.backup.$(date +%Y%m%d_%H%M%S)"
    cp "group_vars/dhcpServers_vault.yml" "$backup_file"
    print_status "OK" "Backed up existing vault file to: $backup_file"
fi

# Prompt for password
echo "Enter a secure password for KEA API (minimum 8 characters):"
read -s kea_password
echo

if [ ${#kea_password} -lt 8 ]; then
    print_status "ERROR" "Password must be at least 8 characters long"
    exit 1
fi

# Create new vault file
cat > group_vars/dhcpServers_vault.yml << EOF
---
# Vault file for sensitive DHCP server variables
# This file contains encrypted sensitive data for DHCP servers

# KEA API Credentials
vault_kea_api_password: "$kea_password"
vault_kea_api_username: "keadm"

# Monitoring Credentials (if using external monitoring)
vault_monitoring_api_key: "monitoring_key_placeholder"

# Backup Encryption Keys
vault_backup_encryption_key: "backup_key_placeholder"

# SNMP Community Strings (if using SNMP monitoring)
vault_snmp_community_ro: "readonly_community"
vault_snmp_community_rw: "readwrite_community"

# Database Credentials (if using external database for leases)
vault_db_username: "kea_user"
vault_db_password: "db_password_placeholder"

# SSL/TLS Certificates (if using HTTPS for control agent)
vault_ssl_cert_path: "/etc/ssl/certs/kea-server.crt"
vault_ssl_key_path: "/etc/ssl/private/kea-server.key"
vault_ssl_ca_path: "/etc/ssl/certs/ca-bundle.crt"

# External Service API Keys
vault_dns_api_key: "dns_api_key_placeholder"
vault_ipam_api_key: "ipam_api_key_placeholder"
EOF

print_status "OK" "Created new vault file with your password"

# Encrypt the vault file
echo "Now encrypting the vault file..."
echo "You will be prompted to create a vault password (this can be the same or different from the KEA API password)"

if ansible-vault encrypt group_vars/dhcpServers_vault.yml; then
    print_status "OK" "Vault file encrypted successfully"
else
    print_status "ERROR" "Failed to encrypt vault file"
    exit 1
fi

echo
echo "Testing the new vault file..."

# Test the vault file
if ansible-vault view group_vars/dhcpServers_vault.yml > /dev/null; then
    print_status "OK" "Vault file can be decrypted"
else
    print_status "ERROR" "Cannot decrypt vault file"
    exit 1
fi

echo
print_status "OK" "Vault file recreation complete!"
echo
echo "Next steps:"
echo "1. Test variable access: ansible-playbook test-vault-vars.yaml --ask-vault-pass"
echo "2. Test DHCP deployment: ansible-playbook dhcpServers.yaml --check --ask-vault-pass"
echo "3. Deploy to servers: ansible-playbook dhcpServers.yaml --ask-vault-pass"
