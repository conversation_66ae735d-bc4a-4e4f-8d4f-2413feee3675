---
# Host-specific variables for silmarillion-002 (Secondary DHCP Server)

# Server Role
dhcp_server_role: "secondary"
kea_ha_role: "standby"

# Network Interface Configuration
static_ip_config:
  ip: "{{ secondary_ip }}"
  netmask: "*************"
  gateway: "{{ network_gateway }}"
  dns_servers:
    - "*******"
    - "{{ primary_ip }}"
    - "{{ secondary_ip }}"

# KEA HA Peer Configuration
kea_ha_this_server_name: "{{ secondary_hostname }}"
kea_ha_peer_url: "http://{{ primary_ip }}:8000/"

# Service Priority
kea_service_priority: "standby"

# Backup Configuration
kea_backup_role: "destination"
kea_backup_source: "{{ primary_ip }}"

# Monitoring Configuration
kea_monitoring_role: "secondary"
kea_stats_collection: false

# Performance Configuration
kea_performance_profile: "standby"
kea_lease_processing_priority: "normal"

# Logging Configuration
kea_log_destination: "/var/log/kea/kea-dhcp4-secondary.log"
kea_audit_log: "/var/log/kea/kea-audit-secondary.log"

# Maintenance Windows
maintenance_window:
  day: "Saturday"
  start_time: "02:00"
  duration_hours: 2

# Health Check Configuration
health_check:
  enabled: true
  interval: 60
  timeout: 15
  retries: 2
  endpoints:
    - "http://localhost:8000/status"
    - "unix:///run/kea/kea4-ctrl-socket"
