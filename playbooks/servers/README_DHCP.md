# DHCP Server Deployment Guide

This directory contains Ansible automation for deploying and managing ISC KEA DHCP servers with high availability support.

## Overview

The DHCP deployment consists of:
- **Primary Server**: `silmarillion-001` (*************)
- **Secondary Server**: `silmarillion-002` (*************)
- **High Availability**: Hot-standby configuration
- **Consolidated Networks**: Subnet 28 (/23) includes former IoT devices

## File Structure

```
playbooks/servers/
├── dhcpServers.yaml              # Main deployment playbook
├── group_vars/
│   ├── dhcpServers.yml           # Group variables
│   └── dhcpServers_vault.yml     # Encrypted sensitive variables
├── host_vars/
│   ├── silmarillion-001.yml     # Primary server variables
│   └── silmarillion-002.yml     # Secondary server variables
├── roles/dhcp-kea/              # KEA DHCP role
└── hosts.yaml                   # Inventory file
```

## Prerequisites

1. **Ansible Requirements**:
   - Ansible 2.9 or higher
   - `community.general` collection
   - `ansible.posix` collection

2. **Target System Requirements**:
   - Debian/Ubuntu or RHEL/Rocky/CentOS
   - Sudo access for deployment user
   - Network connectivity between primary and secondary servers

3. **Network Requirements**:
   - Static IP addresses configured for DHCP servers
   - Firewall ports open (67/udp, 547/udp, 8000/tcp)

## Quick Start

### 1. Configure Sensitive Variables

```bash
# Method 1: Edit existing vault file
ansible-vault edit group_vars/dhcpServers_vault.yml
# Change vault_kea_api_password to a secure password

# Method 2: Create from template
cp group_vars/dhcpServers_vault.yml.example group_vars/dhcpServers_vault.yml
# Edit the file and change the passwords
ansible-vault encrypt group_vars/dhcpServers_vault.yml

# Method 3: Create new vault file
echo "vault_kea_api_password: YourSecurePassword123!" > group_vars/dhcpServers_vault.yml
ansible-vault encrypt group_vars/dhcpServers_vault.yml
```

### 1.1. Test Vault Variable Mapping

```bash
# Test that vault variables are properly mapped
ansible-playbook test-vault-vars.yaml --ask-vault-pass
```

### 2. Deploy DHCP Servers

```bash
# Deploy to all DHCP servers
ansible-playbook dhcpServers.yaml --ask-vault-pass

# Deploy with vault password file
ansible-playbook dhcpServers.yaml --vault-password-file ~/.ansible_vault_pass

# Check mode (dry run)
ansible-playbook dhcpServers.yaml --check --ask-vault-pass
```

### 3. Verify Deployment

```bash
# Check service status
ansible dhcpServers -m service -a "name=kea-dhcp4-server state=started"

# Test API endpoint
curl -u keadm:password -X POST http://*************:8000 \
  -H "Content-Type: application/json" \
  -d '{"command": "status-get"}'
```

## Advanced Usage

### Selective Deployment

```bash
# Deploy only configuration (skip packages)
ansible-playbook dhcpServers.yaml --tags "config"

# Deploy to specific server
ansible-playbook dhcpServers.yaml -l silmarillion-001

# Deploy to test environment
ansible-playbook dhcpServers.yaml -e "target_hosts=dhcpTestServers"
```

### Maintenance Operations

```bash
# Backup configuration
ansible-playbook dhcpServers.yaml --tags "backup"

# Restart services
ansible dhcpServers -m service -a "name=kea-dhcp4-server state=restarted"

# Update only subnet configurations
ansible-playbook dhcpServers.yaml --tags "dhcp,config" --skip-tags "packages"
```

## Configuration Management

### Adding New Subnets

1. Edit `roles/dhcp-kea/defaults/main.yml`
2. Add new subnet to `kea_subnets` list:

```yaml
kea_subnets:
  # ... existing subnets ...
  - id: 32
    name: "new-network"
    description: "New Network Segment"
    subnet: "************/24"
    subnet_mask: "*************"
    gateway: "************"
    dns_servers: "*******, *******"
    valid_lifetime: 86400
    max_valid_lifetime: 259200
    pools:
      - "************00 - **************"
    reservations: []
```

3. Deploy changes:
```bash
ansible-playbook dhcpServers.yaml --tags "config"
```

### Managing Reservations

Add reservations to the appropriate subnet in the `kea_subnets` configuration:

```yaml
reservations:
  - hostname: "new-device"
    hw_address: "aa:bb:cc:dd:ee:ff"
    ip_address: "**************"
    comment: "Optional description"
```

### Modifying Global Settings

Edit `group_vars/dhcpServers.yml` to modify:
- Lease times
- DNS servers
- HA configuration
- Performance settings

## Monitoring and Troubleshooting

### Service Status

```bash
# Check all KEA services
ansible dhcpServers -m shell -a "systemctl status kea-*"

# Check logs
ansible dhcpServers -m shell -a "journalctl -u kea-dhcp4-server -n 50"
```

### API Monitoring

```bash
# Get server status
curl -u keadm:password -X POST http://*************:8000 \
  -H "Content-Type: application/json" \
  -d '{"command": "status-get"}'

# Get lease statistics
curl -u keadm:password -X POST http://*************:8000 \
  -H "Content-Type: application/json" \
  -d '{"command": "statistic-get-all"}'
```

### High Availability Status

```bash
# Check HA status
curl -u keadm:password -X POST http://*************:8000 \
  -H "Content-Type: application/json" \
  -d '{"command": "ha-heartbeat"}'
```

## Security Considerations

1. **Vault Encryption**: All sensitive variables are encrypted with Ansible Vault
2. **API Authentication**: KEA control agent uses basic authentication
3. **Firewall Rules**: Only necessary ports are opened
4. **Service Isolation**: KEA runs with minimal privileges
5. **Backup Encryption**: Configuration backups are secured

## Network Consolidation

The deployment includes a major network consolidation:
- **Previous**: Separate subnet 28 (/24) and subnet 29 (/24)
- **Current**: Consolidated subnet 28 (/23) covering 192.168.28.0/23
- **Benefits**: Simplified management, reduced configuration complexity
- **Migration**: All IoT devices moved from subnet 29 to subnet 28 range

## Troubleshooting

### Common Issues

1. **Vault Variable Shows as UNDEFINED**:
   ```bash
   # Error: Current vault_kea_api_password status: UNDEFINED

   # Step 1: Debug vault file access
   ./debug-vault.sh

   # Step 2: Check if vault file exists and is encrypted
   ls -la group_vars/dhcpServers_vault.yml
   file group_vars/dhcpServers_vault.yml

   # Step 3: Test vault decryption manually
   ansible-vault view group_vars/dhcpServers_vault.yml

   # Step 4: Verify variable names in vault file
   ansible-vault view group_vars/dhcpServers_vault.yml | grep vault_kea_api_password

   # Step 5: Test with simple playbook
   ansible-playbook test-vault-vars.yaml --ask-vault-pass

   # Step 6: If all else fails, recreate vault file
   ./recreate-vault.sh
   ```

2. **Default Password Still in Use**:
   ```bash
   # Error: vault_kea_api_password cannot be the default value

   # Solution: Edit vault file and change password
   ansible-vault edit group_vars/dhcpServers_vault.yml
   # Change: vault_kea_api_password: "CHANGE_ME_BEFORE_ENCRYPTING"
   # To: vault_kea_api_password: "YourSecurePassword123!"
   ```

3. **Service Won't Start**:
   - Check configuration syntax: `kea-dhcp4 -t /etc/kea/kea-dhcp4.conf`
   - Review logs: `journalctl -u kea-dhcp4-server`

4. **HA Not Working**:
   - Verify network connectivity between servers
   - Check API authentication
   - Review HA configuration in logs

5. **Clients Not Getting Leases**:
   - Verify DHCP relay configuration
   - Check subnet pool ranges
   - Review firewall rules

### Log Locations

- **System Logs**: `/var/log/syslog` or `journalctl`
- **KEA Logs**: `/var/log/kea/` (if configured)
- **Audit Logs**: `/var/log/kea/kea-audit.log`

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review KEA documentation: https://kea.readthedocs.io/
3. Check Ansible role documentation in `roles/dhcp-kea/README.md`
