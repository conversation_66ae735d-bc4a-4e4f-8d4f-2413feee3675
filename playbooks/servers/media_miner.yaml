---
# Media Miner Playbook
# 
# This playbook sets up a complete media server with the following components:
# - Music management: Lidarr
# - TV show management: Sonarr
# - Movie management: Radarr
# - Ebook management: Readarr
# - Subtitle management: Bazarr
# - Media encoding: Tdarr/Unmanic/FileFlows
# - Indexer management: Prowlarr
# - Download client: NZBGet
# - Request service: Overseerr

- name: Media Miner - Complete Media Server Setup
  hosts: "{{ hosts | default('moria') }}"
  gather_facts: true
  become: true
  
  vars_files:
    - ./playbook_vars/media_miner.yml
  
  pre_tasks:
    - name: Update package cache
      ansible.builtin.package:
        update_cache: yes
      changed_when: false
      when: ansible_os_family == "Debian"

    - name: Prompt for Docker Hub username
      ansible.builtin.pause:
        prompt: "Enter your Docker Hub username (default: {{ docker_hub_default_user }})"
        echo: yes
      register: docker_username
      when: docker_hub_login | default(true)

    - name: Prompt for Docker Hub password
      ansible.builtin.pause:
        prompt: "Enter your Docker Hub password"
        echo: no
      register: docker_password
      when: docker_hub_login | default(true)

    - name: Login to Docker Hub
      containers.podman.podman_login:
        registry: docker.io
        username: "{{ docker_username.user_input if docker_username.user_input != '' else docker_hub_default_user }}"
        password: "{{ docker_password.user_input }}"
      become: true
      when: docker_hub_login | default(true)
      no_log: true
  
  roles:
    - role: podman-host
      tags: 
        - container
        - setup

    - role: notifiarr
      tags:
        - media
        - notifications
        - monitoring

    - role: media-nzbget
      tags:
        - media
        - download

    - role: media-lidarr
      tags:
        - media
        - music

    - role: media-sonarr
      tags:
        - media
        - tv

    - role: media-radarr
      tags:
        - media
        - movies

    - role: media-prowlarr
      tags:
        - media
        - indexer

    - role: media-jellyseerr
      tags:
        - media
        - requests
      when: enable_jellyseerr

    - role: media-overseerr
      tags:
        - media
        - requests
      when: enable_overseerr

#    - role: media-readarr
#      tags:
#        - media
#        - books
#      when: enable_readarr | default(false)
#
#    - role: media-bazarr
#      tags:
#        - media
#        - subtitles
#      when: enable_bazarr | default(false)
#
#    - role: media-encoding
#      tags:
#        - media
#        - encoding
#      when: enable_encoding | default(false)
#      vars:
#        encoding_app: "{{ encoding_application | default('fileflows') }}"

  post_tasks:
    - name: Verify services are running
      ansible.builtin.command: podman ps
      register: container_status
      changed_when: false
      
    - name: Display container status
      ansible.builtin.debug:
        var: container_status.stdout_lines
        
    - name: Check for container health
      ansible.builtin.uri:
        url: "http://localhost:{{ item.port }}/api/v1/system/status"
        method: GET
        status_code: 200
        timeout: 5
      register: health_check
      failed_when: false
      changed_when: false
      loop:
        - { name: "Sonarr", port: 8989 }
        - { name: "Radarr", port: 7878 }
        - { name: "Lidarr", port: 8686 }
      
    - name: Display service health status
      ansible.builtin.debug:
        msg: "{{ item.item.name }} is {{ 'UP' if item.status == 200 else 'DOWN or STARTING' }}"
      loop: "{{ health_check.results }}"
