#!/bin/bash

# Setup script for init_vault.yml
# This script helps create and encrypt the vault file for initial setup passwords

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored status messages
print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}[OK]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
    esac
}

# Function to generate secure password
generate_password() {
    local length=${1:-16}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Function to validate password strength
validate_password() {
    local password=$1
    local min_length=12
    
    if [ ${#password} -lt $min_length ]; then
        return 1
    fi
    
    # Check for at least one uppercase, lowercase, digit, and special character
    if [[ $password =~ [A-Z] ]] && [[ $password =~ [a-z] ]] && [[ $password =~ [0-9] ]] && [[ $password =~ [^A-Za-z0-9] ]]; then
        return 0
    else
        return 1
    fi
}

echo "========================================="
echo "Init Vault Setup for Combined Playbook"
echo "========================================="

# Change to the correct directory
cd "$(dirname "$0")"
print_status "INFO" "Working directory: $(pwd)"

# Check if example file exists
if [ ! -f "group_vars/init_vault.yml.example" ]; then
    print_status "ERROR" "Example vault file not found: group_vars/init_vault.yml.example"
    exit 1
fi

# Check if vault file already exists
if [ -f "group_vars/init_vault.yml" ]; then
    print_status "WARNING" "Vault file already exists: group_vars/init_vault.yml"
    echo "Do you want to recreate it? This will backup the existing file. (y/N)"
    read -r response
    if [[ ! $response =~ ^[Yy]$ ]]; then
        print_status "INFO" "Exiting without changes"
        exit 0
    fi
    
    # Backup existing file
    backup_file="group_vars/init_vault.yml.backup.$(date +%Y%m%d_%H%M%S)"
    cp "group_vars/init_vault.yml" "$backup_file"
    print_status "OK" "Backed up existing vault file to: $backup_file"
fi

# Copy example file
cp "group_vars/init_vault.yml.example" "group_vars/init_vault.yml"
print_status "OK" "Created vault file from example"

echo ""
print_status "INFO" "Now we'll set up secure passwords for your system"
echo ""

# Collect passwords
echo "=== Initial Setup Passwords ==="
echo "These are the current passwords used for initial connection:"
echo "- aule user password: changeme"
echo "- root password: changeme"
echo ""
echo "Press Enter to continue with these defaults, or type 'custom' to set different values:"
read -r init_choice

if [[ $init_choice == "custom" ]]; then
    echo "Enter current aule password (default: changeme):"
    read -s current_aule_password
    current_aule_password=${current_aule_password:-changeme}
    
    echo "Enter current root password (default: changeme):"
    read -s current_root_password
    current_root_password=${current_root_password:-changeme}
else
    current_aule_password="changeme"
    current_root_password="changeme"
fi

echo ""
echo "=== New Secure Passwords ==="
echo "Now we'll set new secure passwords that will replace the initial ones."
echo ""

# New aule password
while true; do
    echo "Enter new secure password for 'aule' user (or press Enter to generate one):"
    read -s new_aule_password
    
    if [ -z "$new_aule_password" ]; then
        new_aule_password=$(generate_password 16)
        echo "Generated password for aule: $new_aule_password"
        break
    elif validate_password "$new_aule_password"; then
        break
    else
        print_status "ERROR" "Password must be at least 12 characters with uppercase, lowercase, digit, and special character"
    fi
done

# New root password
while true; do
    echo "Enter new secure password for 'root' user (or press Enter to generate one):"
    read -s new_root_password
    
    if [ -z "$new_root_password" ]; then
        new_root_password=$(generate_password 16)
        echo "Generated password for root: $new_root_password"
        break
    elif validate_password "$new_root_password"; then
        break
    else
        print_status "ERROR" "Password must be at least 12 characters with uppercase, lowercase, digit, and special character"
    fi
done

echo ""
echo "=== User Account Passwords ==="
echo "Setting passwords for user accounts (krizzo, srizzo, lrizzo, trizzo, frizzo)..."

declare -A user_passwords
users=("krizzo" "srizzo" "lrizzo" "trizzo" "frizzo")

for user in "${users[@]}"; do
    echo "Enter password for user '$user' (or press Enter to generate one):"
    read -s user_password
    
    if [ -z "$user_password" ]; then
        user_password=$(generate_password 14)
        echo "Generated password for $user: $user_password"
    fi
    
    user_passwords[$user]=$user_password
done

# Update the vault file with the collected passwords
print_status "INFO" "Updating vault file with your passwords..."

# Use sed to replace the passwords in the vault file
sed -i "s/vault_init_aule_password: \"changeme\"/vault_init_aule_password: \"$current_aule_password\"/" group_vars/init_vault.yml
sed -i "s/vault_init_root_password: \"changeme\"/vault_init_root_password: \"$current_root_password\"/" group_vars/init_vault.yml
sed -i "s/vault_new_aule_password: \"NewSecureAulePassword123!\"/vault_new_aule_password: \"$new_aule_password\"/" group_vars/init_vault.yml
sed -i "s/vault_new_root_password: \"NewSecureRootPassword456!\"/vault_new_root_password: \"$new_root_password\"/" group_vars/init_vault.yml

# Update user passwords
for user in "${users[@]}"; do
    old_pattern="${user}: \".*\""
    new_pattern="${user}: \"${user_passwords[$user]}\""
    sed -i "s/$old_pattern/$new_pattern/" group_vars/init_vault.yml
done

print_status "OK" "Vault file updated with your passwords"

# Encrypt the vault file
echo ""
print_status "INFO" "Now encrypting the vault file..."
echo "You will be prompted to create a vault password. Remember this password!"
echo "You'll need it when running the playbook with --ask-vault-pass"

if ansible-vault encrypt group_vars/init_vault.yml; then
    print_status "OK" "Vault file encrypted successfully"
else
    print_status "ERROR" "Failed to encrypt vault file"
    exit 1
fi

# Test the vault file
echo ""
print_status "INFO" "Testing the encrypted vault file..."

if ansible-vault view group_vars/init_vault.yml > /dev/null; then
    print_status "OK" "Vault file can be decrypted successfully"
else
    print_status "ERROR" "Cannot decrypt vault file"
    exit 1
fi

echo ""
echo "========================================="
echo "Vault Setup Complete!"
echo "========================================="
echo ""
print_status "OK" "Vault file created and encrypted: group_vars/init_vault.yml"
echo ""
echo "IMPORTANT: Save these passwords securely!"
echo ""
echo "New aule password: $new_aule_password"
echo "New root password: $new_root_password"
echo ""
echo "User passwords:"
for user in "${users[@]}"; do
    echo "  $user: ${user_passwords[$user]}"
done
echo ""
echo "To run the combined init playbook:"
echo "  ansible-playbook -e \"hosts=testServers\" init_combined.yaml -k -K --ask-vault-pass -v"
echo ""
echo "When prompted:"
echo "  - SSH password: $current_aule_password"
echo "  - BECOME password: $current_root_password"
echo "  - Vault password: [the password you just created]"
echo ""
print_status "INFO" "Setup completed successfully!"
