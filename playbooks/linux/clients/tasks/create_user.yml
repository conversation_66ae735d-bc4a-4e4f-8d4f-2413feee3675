---
# User creation task for Linux client setup
# This task creates individual users with proper configuration

- name: Set user facts for {{ user_config.name }}
  ansible.builtin.set_fact:
    current_user:
      name: "{{ user_config.name }}"
      uid: "{{ user_config.id | int }}"
      gid: "{{ user_config.id | int }}"
      shell: "{{ user_config.shell | default('/bin/bash') }}"
      groups: "{{ user_config.groups | default(['users']) }}"
      create_home: "{{ user_config.create_home | default(true) }}"
      ssh_enabled: "{{ user_config.ssh | default(false) }}"
      ssh_keys: "{{ user_config.ssh_pub_keys | default([]) }}"
      home_dir: "/home/<USER>"
      environment: "{{ user_config.environment | default({}) }}"
    user_exists: "{{ user_config.name in getent_passwd | default({}) }}"
    group_exists: "{{ user_config.name in getent_group | default({}) }}"

- name: Debug user configuration for {{ current_user.name }}
  ansible.builtin.debug:
    msg: |
      Processing user: {{ current_user.name }}
      - UID: {{ current_user.uid }}
      - Shell: {{ current_user.shell }}
      - Groups: {{ current_user.groups | join(', ') }}
      - SSH enabled: {{ current_user.ssh_enabled }}
      - User exists: {{ user_exists }}
      - Group exists: {{ group_exists }}
    verbosity: 1

- name: Create primary group for {{ current_user.name }}
  ansible.builtin.group:
    name: "{{ current_user.name }}"
    gid: "{{ current_user.gid }}"
    state: present
  when: not group_exists
  register: group_creation

- name: Create user {{ current_user.name }}
  ansible.builtin.user:
    name: "{{ current_user.name }}"
    uid: "{{ current_user.uid }}"
    group: "{{ current_user.name }}"
    groups: "{{ current_user.groups | join(',') if current_user.groups else omit }}"
    shell: "{{ current_user.shell }}"
    home: "{{ current_user.home_dir }}"
    create_home: "{{ current_user.create_home }}"
    state: present
    append: true
  register: user_creation

- name: Ensure home directory has correct permissions for {{ current_user.name }}
  ansible.builtin.file:
    path: "{{ current_user.home_dir }}"
    state: directory
    owner: "{{ current_user.name }}"
    group: "{{ current_user.name }}"
    mode: '0755'

- name: Set up SSH directory for {{ current_user.name }}
  ansible.builtin.file:
    path: "{{ current_user.home_dir }}/.ssh"
    state: directory
    owner: "{{ current_user.name }}"
    group: "{{ current_user.name }}"
    mode: '0700'
  when: current_user.ssh_enabled and current_user.ssh_keys | length > 0

- name: Add SSH public keys for {{ current_user.name }}
  ansible.posix.authorized_key:
    user: "{{ current_user.name }}"
    key: "{{ item }}"
    state: present
  loop: "{{ current_user.ssh_keys }}"
  when: current_user.ssh_enabled and current_user.ssh_keys | length > 0

- name: Configure user environment for {{ current_user.name }}
  ansible.builtin.lineinfile:
    path: "{{ current_user.home_dir }}/.bashrc"
    line: "export {{ item.key }}={{ item.value }}"
    regexp: "^export {{ item.key }}="
    state: present
    create: true
    owner: "{{ current_user.name }}"
    group: "{{ current_user.name }}"
    mode: '0644'
  loop: "{{ current_user.environment | dict2items }}"
  when: current_user.environment | length > 0

- name: Configure zsh for {{ current_user.name }}
  block:
    - name: Check if .zshrc already exists for {{ current_user.name }}
      ansible.builtin.stat:
        path: "{{ current_user.home_dir }}/.zshrc"
      register: zshrc_exists

    - name: Create basic .zshrc for {{ current_user.name }}
      ansible.builtin.blockinfile:
        path: "{{ current_user.home_dir }}/.zshrc"
        create: true
        owner: "{{ current_user.name }}"
        group: "{{ current_user.name }}"
        mode: '0644'
        marker: "# {mark} ANSIBLE MANAGED BLOCK - Basic zsh configuration"
        block: |
          # Basic zsh configuration
          autoload -U compinit
          compinit

          # History configuration
          HISTFILE=~/.zsh_history
          HISTSIZE=10000
          SAVEHIST=10000
          setopt SHARE_HISTORY
          setopt HIST_IGNORE_DUPS

          # Aliases
          alias ll='ls -la'
          alias la='ls -A'
          alias l='ls -CF'
          alias vim='nvim'
          alias vi='nvim'

          # Enable colors
          autoload -U colors && colors

          # Simple prompt
          PS1="%{$fg[green]%}%n@%m%{$reset_color%}:%{$fg[blue]%}%~%{$reset_color%}$ "
      when: not zshrc_exists.stat.exists

    - name: Add environment variables to .zshrc for {{ current_user.name }}
      ansible.builtin.lineinfile:
        path: "{{ current_user.home_dir }}/.zshrc"
        line: "export {{ item.key }}={{ item.value }}"
        regexp: "^export {{ item.key }}="
        state: present
        create: true
        owner: "{{ current_user.name }}"
        group: "{{ current_user.name }}"
        mode: '0644'
      loop: "{{ current_user.environment | dict2items }}"
      when: current_user.environment | length > 0
  when: current_user.shell == '/usr/bin/zsh'

- name: Configure sudoers for {{ current_user.name }}
  ansible.builtin.lineinfile:
    path: /etc/sudoers.d/{{ current_user.name }}
    line: "{{ current_user.name }} ALL=(ALL) NOPASSWD:ALL"
    state: present
    create: true
    mode: '0440'
    validate: 'visudo -cf %s'
  when: user_config.sudoer | default(false)

- name: Verify user creation for {{ current_user.name }}
  ansible.builtin.debug:
    msg: "Successfully processed user {{ current_user.name }} with UID {{ current_user.uid }}"
