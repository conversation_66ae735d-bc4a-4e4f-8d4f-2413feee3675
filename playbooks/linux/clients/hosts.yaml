---
# Inventory file for Linux clients
# 
# Usage examples:
#   ansible-playbook -i hosts.yaml linux_client_setup.yaml -e "hosts=linux_clients"
#   ansible-playbook -i hosts.yaml linux_client_setup.yaml -e "hosts=workstations"
#   ansible-playbook -i hosts.yaml linux_client_setup.yaml -e "hosts=laptops"

all:
  children:
    linux_clients:
      children:
        workstations:
          hosts:
            # Example workstation entries
            # workstation-001:
            #   ansible_host: *************
            #   ansible_user: aule
            # workstation-002:
            #   ansible_host: *************
            #   ansible_user: aule
        
        laptops:
          hosts:
            # Example laptop entries
            # laptop-001:
            #   ansible_host: *************
            #   ansible_user: aule
            # laptop-002:
            #   ansible_host: *************
            #   ansible_user: aule
            krizzo-lt-2024-kub-ps:
              ansible_host: **************
              ansible_user: aule
        
        development_machines:
          hosts:
            # Example development machine entries
            # dev-001:
            #   ansible_host: *************
            #   ansible_user: aule
      
      vars:
        # Common variables for all Linux clients
        ansible_user: aule
        ansible_become: true
        ansible_become_method: sudo
        ansible_python_interpreter: auto_silent
        
        # SSH connection settings
        ansible_ssh_common_args: '-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null'
        
        # Timeout settings
        ansible_timeout: 30
        ansible_ssh_timeout: 30
