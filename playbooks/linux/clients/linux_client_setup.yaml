---
# Linux Client Setup Playbook
# 
# This playbook sets up a Linux client with:
# - User 'aule' for automation
# - User 'krizzo' with UID 1985, zsh shell, and neovim as default editor
# - Default package installation
#
# Usage:
#   ansible-playbook -i hosts.yaml linux_client_setup.yaml -e "hosts=linux_clients" -u aule -b

- name: Setup Linux Client
  hosts: "{{ hosts | default('linux_clients') }}"
  gather_facts: true
  become: true
  remote_user: aule

  vars:
    # User configuration
    client_users:
      - name: "aule"
        id: "1000"
        shell: "/bin/bash"
        create_home: true
        ssh: false
        groups: ["sudo"]
        sudoer: true
        sudo_permission: "admin"
      - name: "krizzo"
        id: "1985"
        shell: "/usr/bin/zsh"
        create_home: true
        ssh: true
        ssh_pub_keys:
          - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAtrnC0RHMFtRV5x6hP1ttAyNLQdengHzk1hu0WZUl/U krizzo@krizzo-linux-vm-personal"
          - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIEUmbXta2ZkI/xA8QXuPpHeaU1ROFd8i+/J7b2ywOYlF <EMAIL>-arch-desktop"
          - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIOWeahyBJqrBJrnzm9aMiWC4x02Fg01qMEJp5COZIY+N krizzo@krizzo-win10-desktop-personal"
        groups: ["sudo", "users"]
        sudoer: true
        sudo_permission: "admin"
        environment:
          EDITOR: "nvim"
          LANG: "en_US.UTF-8"

    # Default packages for Linux clients
    linux_client_packages:
      debian:
        - sudo
        - curl
        - wget
        - git
        - neovim
        - zsh
        - tmux
        - htop
        - tree
        - unzip
        - zip
        - rsync
        - ssh
        - openssh-client
        - openssh-server
        - build-essential
        - python3
        - python3-pip
        - apt-transport-https
        - ca-certificates
        - gnupg
        - lsb-release
        - software-properties-common
        - jq
        - bat
        - fd-find
        - ripgrep
        - fzf
        - ncdu
        - dnsutils
        - net-tools
        - tcpdump
        - mtr
        - iptables
        - ufw
        - fail2ban
        - duf
        - tldr
      rhel:
        - sudo
        - curl
        - wget
        - git
        - neovim
        - zsh
        - tmux
        - htop
        - tree
        - unzip
        - zip
        - rsync
        - openssh
        - openssh-clients
        - openssh-server
        - "@Development Tools"
        - python3
        - python3-pip
        - epel-release
        - jq
        - bat
        - fd-find
        - ripgrep
        - fzf
        - ncdu
        - bind-utils
        - net-tools
        - tcpdump
        - mtr
        - iptables
        - firewalld
        - duf
        - tldr

  tasks:
    - name: Display system information
      ansible.builtin.debug:
        msg: |
          Setting up Linux Client:
          - OS Family: {{ ansible_facts['os_family'] }}
          - Distribution: {{ ansible_facts['distribution'] }}
          - Distribution Version: {{ ansible_facts['distribution_version'] }}
          - Architecture: {{ ansible_facts['architecture'] }}
      tags: ['info']

    - name: Set OS family facts
      ansible.builtin.set_fact:
        is_debian_family: "{{ ansible_facts['os_family'] | lower == 'debian' }}"
        is_rhel_family: "{{ ansible_facts['os_family'] | lower in ['redhat', 'rocky', 'centos', 'fedora'] }}"
      tags: ['always']

    - name: Validate supported OS family
      ansible.builtin.fail:
        msg: "Unsupported OS family: {{ ansible_facts['os_family'] }}. This playbook supports Debian and RHEL-based systems only."
      when: not (is_debian_family or is_rhel_family)
      tags: ['validation']

    # Phase 1: Package Installation
    - name: Phase 1 - Install packages on Debian-based systems
      block:
        - name: Update apt cache
          ansible.builtin.apt:
            update_cache: true
            cache_valid_time: 3600
          tags: ['cache']

        - name: Install Debian packages
          ansible.builtin.apt:
            name: "{{ linux_client_packages.debian }}"
            state: present
            update_cache: false
          tags: ['packages']
      when: is_debian_family
      tags: ['packages', 'debian']

    - name: Phase 1 - Install packages on RHEL-based systems
      block:
        - name: Update dnf cache
          ansible.builtin.dnf:
            update_cache: true
          tags: ['cache']

        - name: Install RHEL packages
          ansible.builtin.dnf:
            name: "{{ linux_client_packages.rhel }}"
            state: present
            update_cache: false
          tags: ['packages']
      when: is_rhel_family
      tags: ['packages', 'rhel']

    # Phase 2: User Management
    - name: Phase 2 - Get existing users and groups
      block:
        - name: Get available groups
          ansible.builtin.getent:
            database: group

        - name: Get available users
          ansible.builtin.getent:
            database: passwd
      tags: ['users', 'facts']

    - name: Phase 2 - Create users
      ansible.builtin.include_tasks: tasks/create_user.yml
      loop: "{{ client_users }}"
      loop_control:
        loop_var: user_config
        label: "{{ user_config.name }}"
      tags: ['users', 'create']

    # Phase 3: System Configuration
    - name: Phase 3 - Set neovim as default editor
      ansible.builtin.alternatives:
        name: editor
        path: /usr/bin/nvim
        link: /usr/bin/editor
      when: ansible_facts['os_family'] | lower == 'debian'
      tags: ['editor', 'config']

    - name: Phase 3 - Configure SSH service
      ansible.builtin.systemd:
        name: ssh
        state: started
        enabled: true
      when: is_debian_family
      tags: ['ssh', 'services']

    - name: Phase 3 - Configure SSH service (RHEL)
      ansible.builtin.systemd:
        name: sshd
        state: started
        enabled: true
      when: is_rhel_family
      tags: ['ssh', 'services']

    # Phase 4: Verification
    - name: Phase 4 - Verify user creation
      ansible.builtin.getent:
        database: passwd
        key: "{{ item.name }}"
      loop: "{{ client_users }}"
      loop_control:
        label: "{{ item.name }}"
      register: user_verification
      tags: ['users', 'verification']

    - name: Phase 4 - Display created users
      ansible.builtin.debug:
        msg: "User {{ item.item.name }} created with UID {{ item.ansible_facts.getent_passwd[item.item.name][1] }} and shell {{ item.ansible_facts.getent_passwd[item.item.name][5] }}"
      loop: "{{ user_verification.results }}"
      loop_control:
        label: "{{ item.item.name }}"
      when: item.ansible_facts.getent_passwd[item.item.name] is defined
      tags: ['users', 'verification']

    - name: Phase 4 - Setup complete
      ansible.builtin.debug:
        msg: |
          Linux client setup completed successfully!
          
          Next steps:
          1. Test SSH access: ssh krizzo@{{ inventory_hostname }}
          2. Verify neovim is default editor: echo $EDITOR
          3. Check installed packages: dpkg -l (Debian) or rpm -qa (RHEL)
          
          Users created:
          {% for user in client_users %}
          - {{ user.name }} (UID: {{ user.id }}, Shell: {{ user.shell }})
          {% endfor %}
      tags: ['completion']
